import React, { useMemo, useState } from "react";
import {
	View,
	Text,
	Image,
	Pressable,
	StyleSheet,
} from "react-native";

// Import placeholder image from assets
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

/**
 * VideoCard Component
 *
 * A reusable card component for displaying video items with:
 * - Thumbnail image
 * - Title
 * - Optional metadata (duration, date, etc.)
 * - Placeholder for missing images
 *
 * @param id - Unique identifier for the video
 * @param title - Title of the video
 * @param imageUrl - URL of the thumbnail image
 * @param onPress - Callback for when the card is pressed
 * @param width - Width of the card (default: 280)
 * @param height - Height of the thumbnail (default: 160)
 * @param metadata - Optional metadata to display below the title
 */
interface VideoCardProps {
	id: string;
	title: string;
	imageUrl?: string | null;
	onPress: (id: string, type?: string, eventData?: any) => void;
	width?: number;
	height?: number;
	metadata?: string;
}

const VideoCard: React.FC<VideoCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	width = scale(340),
	height = scale(190),
	metadata,
}) => {
	// Focus state for better TV navigation highlighting
	const [isFocused, setIsFocused] = useState(false);

	// Optimize the image URL to prevent memory issues
	// Using the OPTIMIZED preset which has appropriate dimensions for video cards
	const optimizedImageUrl = useMemo(() => {
		return imageUrl ? optimizeImageUrl(imageUrl, "OPTIMIZED") : null;
	}, [imageUrl]);

	return (
		<Pressable
			style={[
				styles.container,
				{ width },
				isFocused && styles.containerFocused,
			]}
			onPress={() =>
				onPress(id, "video", { name: title, poster: imageUrl })
			}
			onFocus={() => setIsFocused(true)}
			onBlur={() => setIsFocused(false)}
			// Add TV specific props
			hasTVPreferredFocus={false}
			tvParallaxProperties={{ enabled: true }}
			accessible={true}
			accessibilityRole="button"
			accessibilityLabel={`Play video: ${title || "Untitled Video"}`}
		>
			<View
				style={[
					styles.thumbnailContainer,
					{
						width:
							width -
							GLOBAL_STYLES.FOCUS.BORDER_WIDTH * 2 -
							scale(2) * 2,
						height: height - scale(2) * 2,
					},
				]}
			>
				{optimizedImageUrl ? (
					<Image
						source={{ uri: optimizedImageUrl }}
						style={styles.thumbnail}
						resizeMode="cover"
						onError={(error) => {
							console.warn(
								`Image load error for ${title}:`,
								error.nativeEvent
							);
						}}
					/>
				) : (
					<View style={styles.placeholderContainer}>
						<Image
							source={PLACEHOLDER_IMAGE}
							style={styles.thumbnail}
							resizeMode="cover"
						/>
					</View>
				)}
			</View>

			<View style={styles.textContainer}>
				<View style={styles.titleRow}>
					<Text
						style={styles.title}
						numberOfLines={2}
						ellipsizeMode="tail"
					>
						{title || "Untitled Video"}
					</Text>

					{metadata && (
						<Text
							style={styles.metadata}
							numberOfLines={1}
						>
							{/* Extract and display just the duration part from metadata */}
							{metadata.split("•")[0].trim()}
						</Text>
					)}
				</View>
			</View>
		</Pressable>
	);
};

const styles = StyleSheet.create({
	container: {
		borderRadius: scale(10),
		marginRight: GLOBAL_STYLES.ITEM_SPACING,
		// Always have a border to prevent layout shifts - border fits tightly around content
		borderWidth: GLOBAL_STYLES.FOCUS.BORDER_WIDTH,
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_UNFOCUSED, // Transparent by default
		overflow: "hidden", // Ensure content fits within the border
	},
	containerFocused: {
		// White border styling when focused - border fits tightly around element
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_FOCUSED,
		// scale effect  transform: [{ scale: 1.02 }],
	},
	thumbnailContainer: {
		borderRadius: scale(7), // Slightly smaller border radius to account for margin
		overflow: "hidden",
		backgroundColor: "#1a3c6b",
		margin: scale(2), // Add scale(2) spacing between content and border
	},
	thumbnail: {
		width: "100%",
		height: "100%",
	},
	placeholderContainer: {
		width: "100%",
		height: "100%",
		overflow: "hidden",
		backgroundColor: "rgba(26, 60, 107, 0.7)",
	},
	textContainer: {
		marginTop: scale(6), // Reduced top margin to decrease spacing between image and text
		paddingHorizontal: GLOBAL_STYLES.FOCUS.BORDER_WIDTH + scale(2), // Add padding to align text with thumbnail including margin
	},
	titleRow: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "flex-start",
		width: "100%",
	},
	title: {
		fontSize: scale(25),
		color: "#ffffff",
		fontWeight: "bold",
		flex: 1,
		marginRight: scale(12),
		lineHeight: scale(34), // Increased line height for better readability with 2 lines
		textAlign: "left", // Explicitly set left alignment
	},
	metadata: {
		fontSize: scale(23),
		color: "#ffffff",
		fontWeight: "500",
		minWidth: scale(60), // Added minimum width to ensure consistent alignment
		textAlign: "right", // Right-aligned text
	},
});

export default VideoCard;
