import React, { useState } from "react";
import {
	View,
	Pressable,
	Text,
	StyleSheet,
	Modal,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

interface LanguageSelectorProps {
	currentLanguage: string;
	onLanguageChange: (language: string) => void;
	style?: any;
}

const LanguageSelector = ({
	currentLanguage,
	onLanguageChange,
}: LanguageSelectorProps) => {
	// State for dialog visibility and focus management
	const [showDialog, setShowDialog] = useState(false);
	const [isButtonFocused, setIsButtonFocused] = useState(false);
	const [isOkFocused, setIsOkFocused] = useState(true); // OK button focused by default
	const [isCancelFocused, setIsCancelFocused] = useState(false);

	// Get the target language (opposite of current)
	const targetLanguage = currentLanguage === "en" ? "fr" : "en";

	// Dialog messages based on current language
	const dialogMessages = {
		en: {
			message:
				"Do you want to change language and content to French?",
			ok: "OK",
			cancel: "CANCEL",
		},
		fr: {
			message:
				"Voulez-vous changer la langue et le contenu en Anglais?",
			ok: "OK",
			cancel: "ANNULER",
		},
	};

	// Handle language toggle button press
	const handleTogglePress = () => {
		setShowDialog(true);
		setIsOkFocused(true); // Focus OK button by default
		setIsCancelFocused(false);
	};

	// Handle dialog confirmation
	const handleConfirm = () => {
		onLanguageChange(targetLanguage);
		setShowDialog(false);
	};

	// Handle dialog cancellation
	const handleCancel = () => {
		setShowDialog(false);
	};

	return (
		<>
			<Pressable
				style={styles.languageButtonSection}
				onPress={handleTogglePress}
				onFocus={() => setIsButtonFocused(true)}
				onBlur={() => setIsButtonFocused(false)}
				accessible={true}
				accessibilityRole="button"
				accessibilityLabel={`Change language to ${targetLanguage.toUpperCase()}`}
			>
				<View
					style={[
						styles.languageButton,
						isButtonFocused && styles.languageButtonFocused,
					]}
				>
					<Text
						style={[
							styles.languageButtonText,
							isButtonFocused && styles.languageButtonTextFocused,
						]}
					>
						{currentLanguage.toUpperCase()}
					</Text>
				</View>
			</Pressable>

			{/* Language Change Confirmation Dialog */}
			<Modal
				visible={showDialog}
				transparent={true}
				animationType="fade"
				onRequestClose={handleCancel}
			>
				<View style={styles.modalOverlay}>
					<View style={styles.dialogContainer}>
						<Text style={styles.dialogMessage}>
							{
								dialogMessages[
									currentLanguage as keyof typeof dialogMessages
								].message
							}
						</Text>
						<View style={styles.dialogButtons}>
							<Pressable
								style={[
									styles.dialogButton,
									styles.okButton,
									isOkFocused && styles.dialogButtonFocused,
								]}
								onPress={handleConfirm}
								onFocus={() => {
									setIsOkFocused(true);
									setIsCancelFocused(false);
								}}
								onBlur={() => setIsOkFocused(false)}
								hasTVPreferredFocus={true}
								accessible={true}
								accessibilityRole="button"
								accessibilityLabel="Confirm language change"
							>
								<Text
									style={[
										styles.dialogButtonText,
										isOkFocused && styles.dialogButtonTextFocused,
									]}
								>
									{
										dialogMessages[
											currentLanguage as keyof typeof dialogMessages
										].ok
									}
								</Text>
							</Pressable>
							<Pressable
								style={[
									styles.dialogButton,
									styles.cancelButton,
									isCancelFocused && styles.dialogButtonFocused,
								]}
								onPress={handleCancel}
								onFocus={() => {
									setIsCancelFocused(true);
									setIsOkFocused(false);
								}}
								onBlur={() => setIsCancelFocused(false)}
								accessible={true}
								accessibilityRole="button"
								accessibilityLabel="Cancel language change"
							>
								<Text
									style={[
										styles.dialogButtonText,
										isCancelFocused && styles.dialogButtonTextFocused,
									]}
								>
									{
										dialogMessages[
											currentLanguage as keyof typeof dialogMessages
										].cancel
									}
								</Text>
							</Pressable>
						</View>
					</View>
				</View>
			</Modal>
		</>
	);
};

const styles = StyleSheet.create({
	/**
	 * Language Toggle Button Styles
	 */
	languageButtonSection: {
		height: scale(60),
		justifyContent: "center",
		alignItems: "flex-end",
		width: "80%", // make enough width so we can catch focus from anywhere
	},
	languageButton: {
		paddingVertical: scale(8),
		paddingHorizontal: scale(16),
		borderRadius: scale(4),
		borderWidth: scale(2),
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		backgroundColor: "transparent",
		minWidth: scale(60),
	},
	languageButtonFocused: {
		backgroundColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_FOCUSED,
		borderWidth: GLOBAL_STYLES.FOCUS.BORDER_WIDTH,
		// No scaling - keep button the same size on focus
	},
	languageButtonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		fontWeight: "600",
		textAlign: "center",
	},
	languageButtonTextFocused: {
		color: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		fontWeight: "bold",
	},

	/**
	 * Dialog Modal Styles
	 */
	modalOverlay: {
		flex: 1,
		backgroundColor: "rgba(0, 0, 0, 0.8)", // Semi-transparent dark overlay
		justifyContent: "center",
		alignItems: "center",
	},
	dialogContainer: {
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		borderRadius: scale(12),
		padding: scale(32),
		minWidth: scale(600),
		maxWidth: scale(800),
		borderWidth: scale(2),
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		// Removed shadow effects for cleaner appearance
	},
	dialogMessage: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(28),
		fontWeight: "500",
		textAlign: "center",
		marginBottom: scale(32),
		lineHeight: scale(36),
	},
	dialogButtons: {
		flexDirection: "row",
		justifyContent: "center",
		gap: scale(24),
	},
	dialogButton: {
		paddingVertical: scale(12),
		paddingHorizontal: scale(32),
		borderRadius: scale(8),
		borderWidth: scale(2),
		minWidth: scale(120),
	},
	okButton: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderColor: GLOBAL_STYLES.COLORS.ACCENT,
	},
	cancelButton: {
		backgroundColor: "transparent",
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
	},
	dialogButtonFocused: {
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_FOCUSED,
		borderWidth: GLOBAL_STYLES.FOCUS.BORDER_WIDTH,
		// No scaling or shadow effects - clean white border only
	},
	dialogButtonText: {
		fontSize: scale(24),
		fontWeight: "500",
		textAlign: "center",
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
	},
	dialogButtonTextFocused: {
		color: "#ffffff",
		fontWeight: "bold",
	},
});

export default LanguageSelector;
