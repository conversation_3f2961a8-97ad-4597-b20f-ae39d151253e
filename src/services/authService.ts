import { userAPIClient } from "../utils/userApiInstance";
import { setAuthToken } from "../utils/kenticoInstance";
import { setMainApiAuthToken } from "../utils/mainApiInstance";
import { setUserApiAuthToken } from "../utils/userApiInstance";
import { TokenStorage, TokenData } from "./tokenStorage";
import { FirebaseAuth, FirebaseAuthError } from "./firebaseAuth";

// Interface for login credentials
export interface LoginCredentials {
	email: string;
	password: string;
}

// Interface for login response
export interface LoginResponse {
	success: boolean;
	accessToken?: string;
	refreshToken?: string;
	userId?: string;
	expiresIn?: number;
	error?: string;
}

// Interface for API login response (matches the actual API response)
interface ApiLoginResponse {
	id: string;
	accessToken: string;
	refreshToken: string;
	tokenType: string;
	expiresIn: number;
	action: string;
}

/**
 * Authentication Service
 * Handles complete authentication flow with Firebase and token management
 */
export class AuthService {
	/**
	 * Login with email and password
	 * 1. Authenticates with Firebase using email/password
	 * 2. Exchanges Firebase token for app access token
	 * 3. Stores tokens securely
	 * 4. Sets tokens in API clients
	 */
	static async login(
		credentials: LoginCredentials
	): Promise<LoginResponse> {
		try {
			console.log("Starting login process for:", credentials.email);

			// Step 1: Authenticate with Firebase
			const firebaseResult =
				await FirebaseAuth.signInWithEmailAndPassword(
					credentials.email,
					credentials.password
				);

			console.log("Firebase authentication successful");
			console.log(
				"Firebase token (first 50 chars):",
				firebaseResult.token.substring(0, 50) + "..."
			);

			// Step 2: Exchange Firebase token for app access token
			const loginPayload = {
				vendorSSO: {
					name: "firebase" as const,
					token: firebaseResult.token,
				},
			};

			console.log("Sending login request with payload:", {
				vendorSSO: {
					name: loginPayload.vendorSSO.name,
					token:
						loginPayload.vendorSSO.token.substring(0, 50) + "...",
				},
			});

			const response = await userAPIClient.auth.loginFan(
				loginPayload
			);

			// Step 3: Process the API response
			if (response.data && this.isValidApiResponse(response.data)) {
				const apiResponse = response.data as ApiLoginResponse;

				console.log("API login successful:", {
					userId: apiResponse.id,
					tokenType: apiResponse.tokenType,
					expiresIn: apiResponse.expiresIn,
					action: apiResponse.action,
				});

				// Step 4: Store tokens securely
				const tokenData: TokenData = {
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					expiresIn: apiResponse.expiresIn,
					userId: apiResponse.id,
				};

				await TokenStorage.storeTokens(tokenData);

				// Step 5: Set tokens in API clients
				this.setAuthTokens(apiResponse.accessToken);

				console.log("✅ Login completed successfully!");
				console.log(
					"📱 User authenticated and tokens set in API clients"
				);

				return {
					success: true,
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					userId: apiResponse.id,
					expiresIn: apiResponse.expiresIn,
				};
			} else {
				console.log("Invalid API response format:", response.data);
				return {
					success: false,
					error: "Login failed: Invalid response from server",
				};
			}
		} catch (error: any) {
			console.log("Login error:", error);

			// Handle Firebase authentication errors
			if (error.code) {
				const firebaseError = error as FirebaseAuthError;
				return {
					success: false,
					error: firebaseError.message,
				};
			}

			// Handle API errors
			let errorMessage = "Login failed";
			if (error.response?.data?.message) {
				errorMessage = error.response.data.message;
			} else if (error.message) {
				errorMessage = error.message;
			}

			return {
				success: false,
				error: errorMessage,
			};
		}
	}

	/**
	 * Basic email validation - only checks if email is not empty
	 */
	static validateEmail(email: string): boolean {
		return email.trim().length > 0;
	}

	/**
	 * Basic password validation - only checks if password is not empty
	 */
	static validatePassword(password: string): boolean {
		return password.trim().length > 0;
	}

	/**
	 * Refresh access token using stored refresh token
	 */
	static async refreshToken(): Promise<LoginResponse> {
		try {
			const refreshToken = await TokenStorage.getRefreshToken();

			if (!refreshToken) {
				return {
					success: false,
					error: "No refresh token available",
				};
			}

			console.log("Refreshing access token...");

			// Call refresh token API
			const response = await userAPIClient.auth.refreshTokenFan({
				refreshToken: refreshToken,
			});

			if (response.data && this.isValidApiResponse(response.data)) {
				const apiResponse = response.data as ApiLoginResponse;

				console.log("Token refresh successful");

				// Store new tokens
				const tokenData: TokenData = {
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					expiresIn: apiResponse.expiresIn,
					userId: apiResponse.id,
				};

				await TokenStorage.storeTokens(tokenData);

				// Set new access token in API clients
				this.setAuthTokens(apiResponse.accessToken);

				return {
					success: true,
					accessToken: apiResponse.accessToken,
					refreshToken: apiResponse.refreshToken,
					userId: apiResponse.id,
					expiresIn: apiResponse.expiresIn,
				};
			} else {
				console.error(
					"Invalid refresh response format:",
					response.data
				);
				return {
					success: false,
					error: "Token refresh failed: Invalid response from server",
				};
			}
		} catch (error: any) {
			console.error("Token refresh error:", error);

			let errorMessage = "Token refresh failed";
			if (error.response?.data?.message) {
				errorMessage = error.response.data.message;
			} else if (error.message) {
				errorMessage = error.message;
			}

			return {
				success: false,
				error: errorMessage,
			};
		}
	}

	/**
	 * Get current access token, refreshing if necessary
	 */
	static async getValidAccessToken(): Promise<string | null> {
		try {
			// Check if current token is expired
			const isExpired = await TokenStorage.isTokenExpired();

			if (isExpired) {
				console.log("Access token expired, attempting refresh...");
				const refreshResult = await this.refreshToken();

				if (refreshResult.success && refreshResult.accessToken) {
					return refreshResult.accessToken;
				} else {
					console.log(
						"Token refresh failed, user needs to login again"
					);
					await this.logout();
					return null;
				}
			}

			// Return current valid token
			return await TokenStorage.getAccessToken();
		} catch (error) {
			console.error("Error getting valid access token:", error);
			return null;
		}
	}

	/**
	 * Logout user and clear all tokens
	 * Calls the server logout endpoint before clearing local tokens
	 */
	static async logout(): Promise<void> {
		try {
			console.log("Logging out user...");

			// Step 1: Call server logout endpoint to invalidate token on server
			try {
				const accessToken = await TokenStorage.getAccessToken();
				if (accessToken) {
					console.log("Calling server logout endpoint...");
					// Call the DELETE /auth/fan/logout endpoint
					await userAPIClient.auth.logoutFan();
					console.log("Server logout successful");
				} else {
					console.log(
						"No access token found, skipping server logout"
					);
				}
			} catch (serverLogoutError) {
				// Log the error but continue with local cleanup
				// This ensures user can still logout locally even if server call fails
				console.error(
					"Server logout failed, continuing with local cleanup:",
					serverLogoutError
				);
			}

			// Step 2: Clear stored tokens locally
			await TokenStorage.clearTokens();

			// Step 3: Clear tokens from API clients
			this.clearAuthTokens();

			console.log("Logout successful");
		} catch (error) {
			console.error("Error during logout:", error);
		}
	}

	/**
	 * Check if user is currently authenticated
	 */
	static async isAuthenticated(): Promise<boolean> {
		try {
			const accessToken = await TokenStorage.getAccessToken();
			const isExpired = await TokenStorage.isTokenExpired();
			return accessToken !== null && !isExpired;
		} catch (error) {
			console.error("Error checking authentication status:", error);
			return false;
		}
	}

	/**
	 * Initialize authentication on app start
	 * Checks for stored tokens and sets them in API clients
	 */
	static async initializeAuth(): Promise<boolean> {
		try {
			const accessToken = await this.getValidAccessToken();

			if (accessToken) {
				this.setAuthTokens(accessToken);
				console.log("Authentication initialized with valid token");
				return true;
			} else {
				console.log("No valid authentication found");
				return false;
			}
		} catch (error) {
			console.error("Error initializing authentication:", error);
			return false;
		}
	}

	/**
	 * Validate API response structure
	 */
	private static isValidApiResponse(data: any): boolean {
		return (
			data &&
			typeof data === "object" &&
			"id" in data &&
			"accessToken" in data &&
			"refreshToken" in data &&
			"tokenType" in data &&
			"expiresIn" in data
		);
	}

	/**
	 * Set authentication tokens in all API instances
	 */
	private static setAuthTokens(accessToken: string): void {
		setAuthToken(accessToken);
		setMainApiAuthToken(accessToken);
		setUserApiAuthToken(accessToken);
	}

	/**
	 * Clear authentication tokens from all API instances
	 */
	private static clearAuthTokens(): void {
		setAuthToken(null);
		setMainApiAuthToken(null);
		setUserApiAuthToken(null);
	}
}
