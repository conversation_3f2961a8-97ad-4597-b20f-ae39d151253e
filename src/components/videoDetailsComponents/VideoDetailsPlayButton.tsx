import { StyleSheet, View, Text, Pressable } from "react-native";
import React, { memo, useState } from "react";
import { useNavigation } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../app/index";
import { scale } from "../../utils/helpers/dimensionScale.helper";

type VideoPlayerNavigationProp = NativeStackNavigationProp<
	RootStackParamList,
	"VideoPlayer"
>;

interface VideoDetailsPlayButtonProps {
	video: {
		videoId: string;
		title: string;
		thumbnail: string;
		description?: string;
		isLive?: boolean;
	};
}

const VideoDetailsPlayButton = ({
	video,
}: VideoDetailsPlayButtonProps) => {
	const navigation = useNavigation<VideoPlayerNavigationProp>();
	const [isFocused, setIsFocused] = useState(false);

	const buttonText = video.isLive ? "Watch Live" : "▶ Play";

	return (
		<Pressable
			hasTVPreferredFocus={true}
			style={styles.playButtonSection}
			onPress={() => {
				navigation.navigate("VideoPlayer", { video });
			}}
			onFocus={() => setIsFocused(true)}
			onBlur={() => setIsFocused(false)}
			focusable={true}
			accessible={true}
			accessibilityRole="button"
			accessibilityLabel={`${
				video.isLive ? "Watch live stream" : "Play video"
			}: ${video.title}`}
		>
			<View
				style={[
					styles.playButton,
					isFocused && styles.playButtonFocused,
				]}
			>
				<Text
					style={[
						styles.playButtonText,
						isFocused && styles.playButtonTextFocused,
					]}
				>
					{buttonText}
				</Text>
			</View>
		</Pressable>
	);
};

export default memo(VideoDetailsPlayButton);

const styles = StyleSheet.create({
	playButtonSection: {
		height: scale(120),
		justifyContent: "center",
		alignItems: "center",
		marginBottom: scale(80),
		width: "100%", // make full width so we can catch focus from anywhere
	},
	playButton: {
		backgroundColor: "rgba(255, 255, 255, 0.2)",
		paddingHorizontal: scale(40),
		paddingVertical: scale(20),
		borderRadius: scale(33),
		borderWidth: scale(4),
		borderColor: "rgba(255, 255, 255, 0.5)",
		minWidth: scale(200),
	},
	playButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.4)",
		borderColor: "#ffffff",
	},
	playButtonText: {
		color: "#ffffff",
		fontSize: scale(32),
		fontWeight: "bold",
		textAlign: "center",
	},
	playButtonTextFocused: {
		color: "#ffffff",
		fontWeight: "bold",
	},
});
