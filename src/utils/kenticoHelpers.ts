import {
	VideoCard,
	PlaylistCard,
	CategoryCard,
	Image,
	CarouselStaticSection,
	KenticoImageType,
	PageLayout,
	OriginsEventArray,
} from "../utils/apis/generated/kentico";
import { kenticoAPIClient } from "./kenticoInstance";

// Helper function to handle API errors
const handleApiError = (error: any, customMessage: string) => {
	console.error(customMessage, error);
	throw new Error(customMessage);
};

// Fetch and type videos
export const fetchVideos = async (
	limit: number = 10
): Promise<VideoCard[]> => {
	try {
		const response = await kenticoAPIClient.ott.getVideos({ limit });
		if (Array.isArray(response.data)) {
			return response.data.map((item) => ({
				itemId: item.itemId,
				itemType: item.itemType,
				urlSlug: item.urlSlug,
				// ... map other required VideoCard properties
			})) as VideoCard[];
		}
		return [];
	} catch (error) {
		handleApiError(error, "Error fetching videos");
		return [];
	}
};

// Fetch and type playlists
export const fetchPlaylists = async (
	id: string,
	limit: number = 10
): Promise<PlaylistCard[]> => {
	try {
		const response = await kenticoAPIClient.ott.getPlaylistV2(id, {
			limit,
		});
		return response.data as unknown as PlaylistCard[];
	} catch (error) {
		handleApiError(error, "Error fetching playlists");
		return [];
	}
};

// Fetch and type categories
export const fetchCategories = async (): Promise<CategoryCard[]> => {
	try {
		const response = await kenticoAPIClient.ott.indexCategoriesV2();
		return response.data as CategoryCard[];
	} catch (error) {
		handleApiError(error, "Error fetching categories");
		return [];
	}
};

// Fetch and type images
export const fetchImages = async (
	imageData: KenticoImageType
): Promise<Image> => {
	// Assuming images are part of other content and need to be processed
	return {
		_kenticoCodename: "",
		_kenticoId: "",
		_kenticoItemType: "image",
		_kenticoLanguage: "",
		image: imageData,
		title: null,
		description: null,
	};
};

// Fetch carousel sections
export const fetchCarouselSections = async (
	pageId: string
): Promise<CarouselStaticSection[]> => {
	try {
		const response = await kenticoAPIClient.ott.getPage(pageId);
		return response.data.components.filter(
			(component: any) =>
				component._kenticoItemType === "section_static_carousel"
		) as CarouselStaticSection[];
	} catch (error) {
		handleApiError(error, "Error fetching carousel sections");
		return [];
	}
};

/**
 * ----------------------------------------------------------------
 * 3 main functions to fetch data from Kentico
 * for the home page, live events and future events
 *
 */
// Fetch home page data
export const fetchHomePageData = async (
	language: string = "en"
): Promise<PageLayout> => {
	try {
		const response = await kenticoAPIClient.ott.getPage("home", {
			language,
		});
		return response.data;
	} catch (error) {
		handleApiError(error, "Error fetching home page data");
		throw error;
	}
};

/**
 * Validates if a live event is still actively broadcasting
 * Checks both timestamp and broadcast status
 */
export const validateLiveEventStatus = (event: any): boolean => {
	const now = new Date();

	// Get event start and end dates
	const startDate = event.event?.startDate
		? new Date(event.event.startDate)
		: null;
	const endDate = event.event?.endDate
		? new Date(event.event.endDate)
		: null;

	// Check if event has valid start date
	if (!startDate || isNaN(startDate.getTime())) {
		console.warn(
			`Event ${event.event?.name || "unknown"} has invalid start date`
		);
		return false;
	}

	// Check if event has ended (if end date is available)
	if (endDate && !isNaN(endDate.getTime()) && now > endDate) {
		console.log(
			`Event ${
				event.event?.name
			} has ended (endDate: ${endDate.toISOString()})`
		);
		return false;
	}

	// Check if event hasn't started yet
	if (startDate > now) {
		console.log(
			`Event ${
				event.event?.name
			} hasn't started yet (startDate: ${startDate.toISOString()})`
		);
		return false;
	}

	// Check onrewindState for broadcast status
	const onrewindState = event.event?.onrewindState;
	if (onrewindState) {
		// Valid live states that indicate active broadcasting
		const validLiveStates = [
			"liveOn",
			"awsLive",
			"liveDailymotion",
			"liveYoutube",
			"harmonic",
		];
		const isValidState = validLiveStates.includes(onrewindState);

		if (!isValidState) {
			console.log(
				`Event ${event.event?.name} has inactive broadcast state: ${onrewindState}`
			);
			return false;
		}
	}

	return true;
};

// Fetch live events with enhanced validation
export const fetchLiveEvents = async (
	language: string = "en"
): Promise<OriginsEventArray> => {
	try {
		// Include the current date to ensure we get today's live events
		const today = new Date().toISOString().split("T")[0];
		console.log(`Fetching live events for date: ${today}`);

		// Note: language is not a direct parameter for getEventsV2, it's handled at the client level
		const response = await kenticoAPIClient.ott.getEventsV2({
			status: "live",
			date: today,
		});

		// Filter events to only include those that are actually live and broadcasting
		const validLiveEvents = response.data.filter((event) => {
			const isValid = validateLiveEventStatus(event);
			if (!isValid) {
				console.log(
					`Filtering out invalid live event: ${
						event.event?.name || "unknown"
					}`
				);
			}
			return isValid;
		});

		// Log basic info about the fetched events
		console.log(
			`Fetched ${response.data.length} live events from API, ${validLiveEvents.length} are valid and broadcasting`
		);

		return validLiveEvents;
	} catch (error) {
		handleApiError(error, "Error fetching live events");
		throw error;
	}
};

// Fetch future events
export const fetchFutureEvents = async (
	_language: string = "en" // Prefix with underscore to indicate it's not used
): Promise<OriginsEventArray> => {
	try {
		// Set time to start of day for consistent comparison
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		const todayISOString = today.toISOString();

		console.log(
			`Fetching future events from date: ${todayISOString}`
		);

		const response = await kenticoAPIClient.ott.getEventsV2({
			status: "future",
			date: todayISOString,
		});

		// Log basic info about the fetched events
		console.log(`Fetched ${response.data.length} future events`);

		return response.data;
	} catch (error) {
		handleApiError(error, "Error fetching future events");
		throw error;
	}
};
