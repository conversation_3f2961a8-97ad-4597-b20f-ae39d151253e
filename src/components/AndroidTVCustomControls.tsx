// components/AndroidTVCustomControls.tsx

import React, {
	memo,
	useCallback,
	useEffect,
	useRef,
	useState,
} from "react";

import { View, Text, Pressable, StyleSheet } from "react-native";

import { scale } from "../utils/helpers/dimensionScale.helper"; // Adjust path as needed

// ============================================================================

// TYPES

// ============================================================================

export interface AndroidTVCustomControlsProps {
	visible: boolean;

	paused: boolean;

	currentTime: number;

	duration: number;

	buffering: boolean;

	onPlayPause: () => void;

	onRewind: () => void;

	onForward: () => void;

	onSeek: (time: number) => void; // Seek to absolute time

	onSeekStart?: () => void; // Optional: Notify parent seek started

	onSeekEnd?: () => void; // Optional: Notify parent seek ended

	onFocusChange?: (
		element: "playPause" | "rewind" | "forward" | "seekBar" | null
	) => void;

	focusedControl:
		| "playPause"
		| "rewind"
		| "forward"
		| "seekBar"
		| null;

	style?: any; // Allow passing additional styles
}

// ============================================================================

// COMPONENT

// ============================================================================

const AndroidTVCustomControls = memo(
	({
		visible,

		paused,

		currentTime,

		duration,

		buffering,

		onPlayPause,

		onRewind,

		onForward,

		onSeek,

		onSeekStart,

		onSeekEnd,

		onFocusChange,

		focusedControl,

		style,
	}: AndroidTVCustomControlsProps) => {
		// Internal focus states for visual feedback (managed locally)

		const [playPauseFocused, setPlayPauseFocused] = useState(false);

		const [rewindFocused, setRewindFocused] = useState(false);

		const [forwardFocused, setForwardFocused] = useState(false);

		const [seekBarFocused, setSeekBarFocused] = useState(false);

		// Refs for programmatic focus control (if needed by parent)

		const playPauseButtonRef = useRef<any>(null);

		const rewindButtonRef = useRef<any>(null);

		const forwardButtonRef = useRef<any>(null);

		// Reset focus when controls become visible

		useEffect(() => {
			if (!visible) {
				setPlayPauseFocused(false);

				setRewindFocused(false);

				setForwardFocused(false);

				setSeekBarFocused(false);

				onFocusChange?.(null);
			}
		}, [visible, onFocusChange]);

		const formatTime = useCallback((seconds: number) => {
			const mins = Math.floor(seconds / 60);

			const secs = Math.floor(seconds % 60);

			return `${mins.toString().padStart(2, "0")}:${secs
				.toString()
				.padStart(2, "0")}`;
		}, []);

		// Handle focus changes for visual feedback

		const handleRewindFocus = useCallback(() => {
			setRewindFocused(true);

			setPlayPauseFocused(false);

			setForwardFocused(false);

			setSeekBarFocused(false);

			onFocusChange?.("rewind");
		}, [onFocusChange]);

		const handlePlayPauseFocus = useCallback(() => {
			setPlayPauseFocused(true);

			setRewindFocused(false);

			setForwardFocused(false);

			setSeekBarFocused(false);

			onFocusChange?.("playPause");
		}, [onFocusChange]);

		const handleForwardFocus = useCallback(() => {
			setForwardFocused(true);

			setPlayPauseFocused(false);

			setRewindFocused(false);

			setSeekBarFocused(false);

			onFocusChange?.("forward");
		}, [onFocusChange]);

		const handleSeekBarFocus = useCallback(() => {
			setSeekBarFocused(true);

			setPlayPauseFocused(false);

			setRewindFocused(false);

			setForwardFocused(false);

			onFocusChange?.("seekBar");
		}, [onFocusChange]);

		// Simplified press handlers

		const handleRewindPress = useCallback(() => {
			console.log("[AndroidTVCustomControls] Rewind button pressed");

			onRewind();
		}, [onRewind]);

		const handleForwardPress = useCallback(() => {
			console.log("[AndroidTVCustomControls] Forward button pressed");

			onForward();
		}, [onForward]);

		// Seek Bar Interaction - Calculate seek time based on tap position

		const handleSeekBarPress = useCallback(
			(event: any) => {
				if (duration <= 0) return;

				const locationX = event.nativeEvent.locationX;

				// Use measure to get the actual width of the seek bar
				event.currentTarget.measure(
					(
						_fx: number,
						_fy: number,
						width: number,
						_height: number
					) => {
						const seekTime = (locationX / width) * duration;

						onSeek(Math.max(0, Math.min(duration, seekTime))); // Clamp seek time
					}
				);
			},

			[duration, onSeek]
		);

		// Seek Bar Interaction - Start dragging (if needed)

		const handleSeekBarStart = useCallback(() => {
			onSeekStart?.();
		}, [onSeekStart]);

		// Seek Bar Interaction - End dragging (if needed)

		const handleSeekBarEnd = useCallback(() => {
			onSeekEnd?.();
		}, [onSeekEnd]);

		if (!visible) return null;

		return (
			<View style={[styles.customControlsOverlay, style]}>
				<View style={styles.controlsContainer}>
					{/* Rewind Button */}

					<Pressable
						ref={rewindButtonRef}
						style={[
							styles.controlButton,

							(focusedControl === "rewind" || rewindFocused) &&
								styles.controlButtonFocused,
						]}
						onPress={handleRewindPress}
						onFocus={handleRewindFocus}
						onBlur={() => setRewindFocused(false)}
						hasTVPreferredFocus={focusedControl === "rewind"}
						focusable={true}
						accessible={true}
						accessibilityRole="button"
						accessibilityLabel="Rewind 10 seconds"
					>
						<Text style={styles.controlText}>{"⏪"}</Text>
					</Pressable>

					{/* Play/Pause Button */}

					<Pressable
						ref={playPauseButtonRef}
						style={[
							styles.playPauseButton,

							(focusedControl === "playPause" || playPauseFocused) &&
								styles.playPauseButtonFocused,
						]}
						onPress={onPlayPause}
						onFocus={handlePlayPauseFocus}
						onBlur={() => setPlayPauseFocused(false)}
						hasTVPreferredFocus={focusedControl === "playPause"}
						focusable={true}
						accessible={true}
						accessibilityRole="button"
						accessibilityLabel={paused ? "Play video" : "Pause video"}
					>
						<Text style={styles.playPauseText}>
							{paused ? "▶" : "⏸"}
						</Text>
					</Pressable>

					{/* Fast Forward Button */}

					<Pressable
						ref={forwardButtonRef}
						style={[
							styles.controlButton,

							(focusedControl === "forward" || forwardFocused) &&
								styles.controlButtonFocused,
						]}
						onPress={handleForwardPress}
						onFocus={handleForwardFocus}
						onBlur={() => setForwardFocused(false)}
						hasTVPreferredFocus={focusedControl === "forward"}
						focusable={true}
						accessible={true}
						accessibilityRole="button"
						accessibilityLabel="Fast forward 10 seconds"
					>
						<Text style={styles.controlText}>{"⏩"}</Text>
					</Pressable>
				</View>

				{/* Progress Bar Container */}

				<View style={styles.progressContainer}>
					<Text style={styles.timeText}>
						{formatTime(currentTime)}
					</Text>

					{/* Seek Bar */}

					<Pressable
						onPress={handleSeekBarPress} // Use onPress for tap-based seeking
						// onStartShouldSetResponder={handleSeekBarStart} // For drag (requires more complex handling)

						// onResponderRelease={handleSeekBarEnd} // For drag (requires more complex handling)

						style={[
							styles.seekBar,

							seekBarFocused && styles.seekBarFocused,
						]}
						onFocus={handleSeekBarFocus}
						onBlur={() => setSeekBarFocused(false)}
						focusable={true}
						accessible={true}
						accessibilityRole="adjustable"
						accessibilityLabel="Seek video"
					>
						<View style={styles.seekBarTrack}>
							<View
								style={[
									styles.seekBarProgress,

									{
										width:
											duration > 0
												? `${(currentTime / duration) * 100}%`
												: "0%",
									},
								]}
							/>
						</View>
					</Pressable>

					<Text style={styles.timeText}>{formatTime(duration)}</Text>
				</View>

				{/* Buffering Indicator */}

				{buffering && (
					<View style={styles.bufferingIndicator}>
						<Text style={styles.bufferingText}>Buffering...</Text>
					</View>
				)}
			</View>
		);
	}
);

// ============================================================================

// STYLES (Extracted and slightly adjusted)

// ============================================================================

const styles = StyleSheet.create({
	customControlsOverlay: {
		...StyleSheet.absoluteFillObject,

		backgroundColor: "rgba(0, 0, 0, 0.6)",

		justifyContent: "flex-end",

		paddingBottom: scale(40),

		paddingHorizontal: scale(40),
	},

	controlsContainer: {
		flexDirection: "row",

		alignItems: "center",

		justifyContent: "center",

		marginBottom: scale(20),
	},

	controlButton: {
		backgroundColor: "rgba(255, 255, 255, 0.2)",

		borderRadius: scale(25),

		width: scale(50),

		height: scale(50),

		justifyContent: "center",

		alignItems: "center",

		marginHorizontal: scale(10),

		borderWidth: 2,

		borderColor: "transparent",
	},

	controlButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.4)",

		borderColor: "#fff",

		transform: [{ scale: 1.1 }],
	},

	controlText: {
		color: "#fff",

		fontSize: scale(20),

		fontWeight: "bold",
	},

	playPauseButton: {
		backgroundColor: "rgba(255, 255, 255, 0.2)",

		borderRadius: scale(35),

		width: scale(70),

		height: scale(70),

		justifyContent: "center",

		alignItems: "center",

		marginHorizontal: scale(15),

		borderWidth: 2,

		borderColor: "transparent",
	},

	playPauseButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.4)",

		borderColor: "#fff",

		transform: [{ scale: 1.1 }],
	},

	playPauseText: {
		color: "#fff",

		fontSize: scale(28),

		fontWeight: "bold",
	},

	progressContainer: {
		flexDirection: "row",

		alignItems: "center",

		width: "100%",
	},

	timeText: {
		color: "#fff",

		fontSize: scale(14),

		fontWeight: "600",

		minWidth: scale(50),

		textAlign: "center",
	},

	seekBar: {
		flex: 1,

		height: scale(40),

		justifyContent: "center",

		marginHorizontal: scale(15),

		borderRadius: scale(4),

		borderWidth: 2,

		borderColor: "transparent",
	},

	seekBarFocused: {
		borderColor: "#fff",

		backgroundColor: "rgba(255, 255, 255, 0.1)",
	},

	seekBarTrack: {
		height: scale(6),

		backgroundColor: "rgba(255, 255, 255, 0.3)",

		borderRadius: scale(3),

		overflow: "hidden",
	},

	seekBarProgress: {
		height: "100%",

		backgroundColor: "#fff",

		borderRadius: scale(3),
	},

	bufferingIndicator: {
		position: "absolute",

		top: scale(20),

		alignSelf: "center",

		backgroundColor: "rgba(0, 0, 0, 0.5)",

		padding: scale(10),

		borderRadius: scale(5),
	},

	bufferingText: {
		color: "#fff",

		fontSize: scale(16),
	},
});

export default AndroidTVCustomControls;
