import React, { useMemo } from "react";
import {
	StyleSheet,
	Pressable,
	Image,
	Linking,
	Dimensions,
	View,
	Platform,
} from "react-native";
import { GLOBAL_STYLES } from "../../styles/globalStyles";
import { optimizeImageUrl } from "../../utils/helpers/imageOptimization.helper";
import { scale } from "../../utils/helpers/dimensionScale.helper";

interface AdBannerProps {
	imageUrl: string;
	mobileImageUrl?: string;
	redirectionTarget: string;
	accessibilityLabel?: string;
}

/**
 * AdBanner Component
 *
 * A reusable banner component for displaying advertisements
 * Supports both desktop and mobile image variants
 */
const AdBanner: React.FC<AdBannerProps> = ({
	imageUrl,
	mobileImageUrl,
	redirectionTarget,
	accessibilityLabel = "Advertisement",
}) => {
	const [isFocused, setIsFocused] = React.useState(false);
	const windowWidth = Dimensions.get("window").width;
	// Use a fixed height ratio that matches the reference image (purple banner with logos)
	const heightMultiplier = 0.15; // Banner height as percentage of screen width

	// Optimize the image URL to prevent memory issues
	const optimizedImageUrl = useMemo(() => {
		return imageUrl
			? optimizeImageUrl(imageUrl, {
					width: windowWidth, // Use full screen width
					height: Math.round(windowWidth * heightMultiplier),
					quality: 90, // High quality for ad banners
					fit: "crop", // Ensure the image covers the entire area
			  })
			: null;
	}, [imageUrl, windowWidth, heightMultiplier]);

	// Handle banner click/press events
	const handlePress = async () => {
		// Prevent opening URLs on TV platforms
		if (Platform.isTV) {
			console.log(
				"[AdBanner] URL redirection is disabled on TV platforms."
			);
			return;
		}

		console.log(
			"[AdBanner] Banner pressed with target:",
			redirectionTarget
		);
		if (redirectionTarget) {
			try {
				await Linking.openURL(redirectionTarget);
			} catch (error) {
				console.error("[AdBanner] Error opening URL:", error);
			}
		}
	};

	// Calculate banner height for proper spacing
	const bannerHeight = windowWidth * heightMultiplier;

	return (
		<View
			style={[
				styles.container,
				{ height: bannerHeight, width: windowWidth },
			]}
		>
			<Pressable
				style={[
					styles.touchable,
					{
						height: bannerHeight,
						width: windowWidth, // Use exact screen width
					},
					isFocused && styles.touchableFocused,
				]}
				onPress={handlePress}
				onFocus={() => setIsFocused(true)}
				onBlur={() => setIsFocused(false)}
				accessible={true}
				accessibilityLabel={accessibilityLabel}
				accessibilityRole="button"
				// Remove disabled to allow focus #TODO check if it doesn't break the app
			>
				<Image
					source={{ uri: optimizedImageUrl || imageUrl }}
					style={[
						styles.image,
						{
							height: bannerHeight,
							width: windowWidth, // Use exact screen width
						},
					]}
					resizeMode="cover"
				/>
			</Pressable>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		// Width is set dynamically in the component
		marginVertical: 0, // Remove vertical margins to prevent spacing issues
		overflow: "hidden",
	},
	touchable: {
		// Width is set dynamically in the component
		overflow: "hidden", // Clip image to touchable bounds
		borderRadius: scale(10), // Add border radius for consistency
		// Always have a border to prevent layout shifts
		borderWidth: GLOBAL_STYLES.FOCUS.BORDER_WIDTH,
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_UNFOCUSED,
	},
	touchableFocused: {
		// White border styling when focused
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_FOCUSED,
	},
	image: {
		// Width is set dynamically in the component
	},
});

export default AdBanner;
