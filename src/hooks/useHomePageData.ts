import { useState, useEffect, useCallback, useRef } from "react";
import { AppState, AppStateStatus } from "react-native";
import { kenticoAPIClient } from "../utils/kenticoInstance";
import {
	PageLayout,
	SectionContent,
	OriginsEvent,
} from "../utils/apis/generated/kentico";
import {
	fetchLiveEvents,
	fetchFutureEvents,
} from "../utils/kenticoHelpers";

// Cache duration in milliseconds (30 seconds)
const CACHE_DURATION = 30 * 1000;

// Cache storage
interface CacheItem {
	data: PageLayout;
	timestamp: number;
}

const cache: Record<string, CacheItem> = {};

/**
 * Custom hook for fetching home page data
 *
 * Features:
 * - Fetches home page data from Kentico API
 * - Implements in-memory caching with 5-minute expiration
 * - Handles loading and error states
 * - Returns sections, loading state, and error state
 *
 * @param language - The language to fetch content in (default: "en")
 * @returns Object containing sections, loading state, and error state
 */
export const useHomePageData = (language: string = "en") => {
	const [sections, setSections] = useState<SectionContent[]>([]);
	const [liveEvents, setLiveEvents] = useState<OriginsEvent[]>([]);
	const [upcomingEvents, setUpcomingEvents] = useState<
		OriginsEvent[]
	>([]);
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);

	// Track app state to refresh data when app comes back from background
	const appState = useRef(AppState.currentState);
	const lastBackgroundTime = useRef<number | null>(null);

	/**
	 * Fetch home page data from API or cache
	 */
	const fetchData = useCallback(async () => {
		setIsLoading(true);
		setError(null);

		const cacheKey = `home_${language}`;
		const now = Date.now();

		try {
			// Check if we have valid cached data
			if (
				cache[cacheKey] &&
				now - cache[cacheKey].timestamp < CACHE_DURATION
			) {
				console.log("Using cached home page data");
				setSections(cache[cacheKey].data.components);

				// Always fetch fresh live events regardless of cache status
				// Live events change frequently and must be current to avoid stale content
				try {
					console.log(
						"Fetching fresh live events (cache bypass for live content)"
					);
					// Fetch live events - these are events happening right now
					const liveEventsData = await fetchLiveEvents(language);
					console.log(
						`Fetched ${liveEventsData.length} live events from API`
					);
					setLiveEvents(liveEventsData);

					// Fetch upcoming events - these are events scheduled for the future
					const upcomingEventsData = await fetchFutureEvents(
						language
					);
					console.log(
						`Fetched ${upcomingEventsData.length} upcoming events from API`
					);
					setUpcomingEvents(upcomingEventsData);
				} catch (eventErr) {
					console.error(
						"Error fetching events with cached data:",
						eventErr
					);
					// Don't set error state here, as we still have the main page data
				}

				setIsLoading(false);
				return;
			}

			// Fetch fresh data from API
			console.log("Fetching fresh home page data");

			// Use Promise.all to fetch all data in parallel
			const [pageResponse, liveEventsData, upcomingEventsData] =
				await Promise.all([
					kenticoAPIClient.ott.getPage("home", {
						language,
						previewFlag: false,
					}),
					fetchLiveEvents(language),
					fetchFutureEvents(language),
				]);

			// Cache the page response
			cache[cacheKey] = {
				data: pageResponse.data,
				timestamp: now,
			};

			// Update state with all fetched data
			setSections(pageResponse.data.components);
			setLiveEvents(liveEventsData);
			setUpcomingEvents(upcomingEventsData);
		} catch (err) {
			console.error("Error fetching home page data:", err);
			setError("Failed to load home page content");
		} finally {
			setIsLoading(false);
		}
	}, [language]);

	// Refresh live events data when app comes back from background
	const refreshLiveEventsData = useCallback(async () => {
		try {
			console.log(
				"Refreshing live events data after background resume..."
			);

			// Fetch fresh live and upcoming events
			const [liveEventsData, upcomingEventsData] = await Promise.all([
				fetchLiveEvents(language),
				fetchFutureEvents(language),
			]);

			console.log(
				`Refreshed ${liveEventsData.length} live events and ${upcomingEventsData.length} upcoming events`
			);
			setLiveEvents(liveEventsData);
			setUpcomingEvents(upcomingEventsData);
		} catch (error) {
			console.error("Error refreshing live events data:", error);
			// Don't set error state here as this is a background refresh
		}
	}, [language]);

	// Handle app state changes to refresh data when coming back from background
	useEffect(() => {
		const handleAppStateChange = (nextAppState: AppStateStatus) => {
			console.log(
				`App state changed from ${appState.current} to ${nextAppState}`
			);

			if (
				appState.current === "background" &&
				nextAppState === "active"
			) {
				// App is coming back from background
				const now = Date.now();
				const timeSinceBackground = lastBackgroundTime.current
					? now - lastBackgroundTime.current
					: 0;

				console.log(
					`App resumed from background after ${Math.round(
						timeSinceBackground / 1000
					)} seconds`
				);

				// Refresh live events data if app was in background for more than 30 seconds
				// This prevents unnecessary refreshes for quick app switches
				if (timeSinceBackground > 30000) {
					refreshLiveEventsData();
				}
			} else if (nextAppState === "background") {
				// App is going to background, record the time
				lastBackgroundTime.current = Date.now();
				console.log("App going to background, recording timestamp");
			}

			appState.current = nextAppState;
		};

		// Add the app state change listener
		const subscription = AppState.addEventListener(
			"change",
			handleAppStateChange
		);

		// Cleanup function to remove the listener
		return () => {
			subscription?.remove();
		};
	}, [refreshLiveEventsData]);

	// Fetch data on mount and when language changes
	useEffect(() => {
		fetchData();
	}, [fetchData]);

	return {
		sections,
		liveEvents,
		upcomingEvents,
		isLoading,
		error,
		refetch: fetchData,
		refreshLiveEvents: refreshLiveEventsData, // Expose refresh function for manual use
	};
};
