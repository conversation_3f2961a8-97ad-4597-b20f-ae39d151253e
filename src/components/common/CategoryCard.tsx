import React, { useState } from "react";
import {
	View,
	Text,
	Image,
	Pressable,
	StyleSheet,
	StyleProp,
	ViewStyle,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import {
	optimizeImageUrl,
	PRESETS,
} from "../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

// Default placeholder image
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");

interface CategoryCardProps {
	id: string;
	title: string;
	imageUrl?: string;
	onPress: () => void;
	style?: StyleProp<ViewStyle>;
}

/**
 * CategoryCard Component
 *
 * Displays a category with an image and title
 * Used in grid layouts for categories, clubs, etc.
 */
const CategoryCard: React.FC<CategoryCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	style,
}) => {
	// Focus state for better TV navigation highlighting
	const [isFocused, setIsFocused] = useState(false);

	// Optimize image URL if available
	const optimizedImageUrl = imageUrl
		? optimizeImageUrl(imageUrl, PRESETS.CATEGORY_THUMBNAIL)
		: null;

	return (
		<Pressable
			style={[
				styles.container,
				style,
				isFocused && styles.containerFocused,
			]}
			onPress={onPress}
			onFocus={() => setIsFocused(true)}
			onBlur={() => setIsFocused(false)}
			accessible={true}
			accessibilityLabel={`Category: ${title}`}
			accessibilityRole="button"
		>
			<View style={styles.imageContainer}>
				<Image
					source={
						optimizedImageUrl
							? { uri: optimizedImageUrl }
							: PLACEHOLDER_IMAGE
					}
					style={styles.image}
					resizeMode="cover"
				/>
			</View>
			<Text
				style={styles.title}
				numberOfLines={2}
				ellipsizeMode="tail"
			>
				{title}
			</Text>
		</Pressable>
	);
};

const styles = StyleSheet.create({
	container: {
		width: scale(200), // Increased width for bigger cards
		borderRadius: scale(10), // Slightly increased border radius
		backgroundColor: "transparent", // Removed background color for cleaner look
		// Always have a border to prevent layout shifts - border fits tightly around content
		borderWidth: GLOBAL_STYLES.FOCUS.BORDER_WIDTH,
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_UNFOCUSED, // Transparent by default
		overflow: "hidden", // Ensure content fits within the border
	},
	containerFocused: {
		// White border styling when focused - border fits tightly around element
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_FOCUSED,
	},
	imageContainer: {
		width:
			scale(200) -
			GLOBAL_STYLES.FOCUS.BORDER_WIDTH * 2 -
			scale(2) * 2, // Adjust width to account for border and margin
		height: scale(140) - scale(2) * 2, // Adjust height to account for margin
		borderRadius: scale(7), // Slightly smaller border radius to account for margin
		overflow: "hidden",
		backgroundColor: "#1a3c6b", // Background color when no image is available
		margin: scale(2), // Add scale(2) spacing between content and border
	},
	image: {
		width: "100%",
		height: "100%",
	},
	title: {
		fontSize: scale(18), // Increased font size
		fontWeight: "500",
		color: "white",
		padding: scale(12), // Increased padding
		textAlign: "left", // Left-aligned text as requested
		marginTop: scale(6), // Added top margin
		paddingHorizontal: GLOBAL_STYLES.FOCUS.BORDER_WIDTH + scale(2), // Add padding to align title with image content including margin
	},
});

export default React.memo(CategoryCard);
