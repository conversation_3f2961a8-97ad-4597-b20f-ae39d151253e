import React, { useState } from "react";
import {
	View,
	Text,
	Image,
	Pressable,
	StyleSheet,
} from "react-native";

// Import placeholder image from assets
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

/**
 * LiveVideoCard Component
 *
 * A specialized card component for displaying live/upcoming events with:
 * - White border around the card
 * - Full-width thumbnail image inside the border
 * - White bar at the bottom with date and time metadata
 * - Title below the card
 *
 * @param id - Unique identifier for the video
 * @param title - Title of the video
 * @param imageUrl - URL of the thumbnail image
 * @param onPress - Callback for when the card is pressed
 * @param width - Width of the card
 * @param height - Height of the thumbnail
 * @param date - Date text to display (e.g., "Friday 2", "Today", "Tomorrow")
 * @param time - Time text to display (e.g., "19:50")
 * @param isLive - Whether this is a live event
 */
interface LiveVideoCardProps {
	id: string;
	title: string;
	imageUrl?: string | null;
	onPress: (id: string, type?: string, eventData?: any) => void;
	width?: number;
	height?: number;
	date?: string;
	time?: string;
	isLive?: boolean;
}

const LiveVideoCard: React.FC<LiveVideoCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	width = scale(280),
	height = scale(160),
	date,
	time,
	isLive = false,
}) => {
	// Focus state for better TV navigation highlighting
	const [isFocused, setIsFocused] = useState(false);

	// Optimize the image URL to prevent memory issues
	const optimizedImageUrl = imageUrl
		? optimizeImageUrl(imageUrl, "OPTIMIZED")
		: null;

	// Extract date and time from metadata if provided
	const dateText = date || "";
	const timeText = time || "";

	return (
		<Pressable
			style={[
				styles.container,
				{ width },
				isFocused && styles.containerFocused,
			]}
			onPress={() =>
				onPress(id, isLive ? "live-event" : "event", {
					name: title,
					poster: imageUrl,
					isLive,
				})
			}
			onFocus={() => setIsFocused(true)}
			onBlur={() => setIsFocused(false)}
			// Add TV specific props
			hasTVPreferredFocus={false}
			tvParallaxProperties={{ enabled: true }}
			accessible={true}
			accessibilityRole="button"
			accessibilityLabel={`${isLive ? "Live" : "Upcoming"} match: ${
				title || "Untitled Match"
			}`}
		>
			{/* Card with white border */}
			<View
				style={[
					styles.cardContainer,
					{
						width:
							width -
							GLOBAL_STYLES.FOCUS.BORDER_WIDTH * 2 -
							scale(2) * 2,
						height: height + scale(40) - scale(2) * 2,
						margin: scale(2), // scale(2) spacing between content and border
					},
				]}
			>
				{/* Thumbnail container - full width of inner area */}
				<View style={styles.thumbnailContainer}>
					{optimizedImageUrl ? (
						<Image
							source={{ uri: optimizedImageUrl }}
							style={styles.thumbnail}
							resizeMode="cover"
							onError={(error) => {
								console.warn(
									`Image load error for ${title}:`,
									error.nativeEvent
								);
							}}
						/>
					) : (
						<Image
							source={PLACEHOLDER_IMAGE}
							style={styles.thumbnail}
							resizeMode="cover"
						/>
					)}

					{/* Live indicator badge removed - redundant since users already know it's live */}
				</View>

				{/* Metadata bar (thicker bottom border) */}
				<View style={styles.metadataBar}>
					<Text style={styles.dateText}>{dateText}</Text>
					<Text style={styles.timeText}>{timeText}</Text>
				</View>
			</View>

			{/* Title below the card */}
			<View style={styles.titleContainer}>
				<Text
					style={styles.title}
					numberOfLines={1}
					ellipsizeMode="tail"
				>
					{title || "Untitled Match"}
				</Text>
			</View>
		</Pressable>
	);
};

const styles = StyleSheet.create({
	container: {
		borderRadius: scale(10),
		marginRight: scale(16),
		marginBottom: scale(20),
		// Always have a border to prevent layout shifts - border fits tightly around content
		borderWidth: GLOBAL_STYLES.FOCUS.BORDER_WIDTH,
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_UNFOCUSED, // Transparent by default
		overflow: "hidden", // Ensure content fits within the border
	},
	containerFocused: {
		// White border styling when focused - border fits tightly around element
		borderColor: GLOBAL_STYLES.FOCUS.BORDER_COLOR_FOCUSED,
		// Remove scale effect to prevent border clipping issues
	},
	cardContainer: {
		borderWidth: scale(6),
		borderColor: "#ffffff",
		borderRadius: scale(8), // Slightly larger border radius to account for margin
		overflow: "hidden",
		flexDirection: "column",
	},
	thumbnailContainer: {
		width: "100%",
		flex: 1, // Use flex to take remaining space
		position: "relative", // For absolute positioning of overlays
	},
	thumbnail: {
		width: "100%",
		height: "100%",
	},
	// Live indicator styles removed - no longer needed
	metadataBar: {
		width: "100%",
		height: scale(40),
		backgroundColor: "#ffffff",
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		paddingHorizontal: scale(5),
	},
	dateText: {
		fontSize: scale(22),
		color: "#000000",
		fontWeight: "500",
	},
	timeText: {
		fontSize: scale(20),
		color: "#000000",
		fontWeight: "500",
	},
	titleContainer: {
		marginTop: scale(8),
		width: "100%",
		paddingHorizontal: GLOBAL_STYLES.FOCUS.BORDER_WIDTH + scale(2), // Add padding to align title with card content including margin
	},
	title: {
		fontSize: scale(28),
		paddingVertical: scale(12),
		paddingLeft: scale(4),
		color: "#ffffff",
		fontWeight: "bold",
		lineHeight: scale(24),
	},
});

export default LiveVideoCard;
