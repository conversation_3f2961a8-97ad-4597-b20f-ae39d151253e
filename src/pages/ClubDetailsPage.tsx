import React, { useEffect, useRef } from "react";
import {
	StyleSheet,
	Text,
	View,
	FlatList,
	Image,
	Pressable,
	ActivityIndicator,
	ImageStyle,
	StyleProp,
} from "react-native";
import {
	useNavigation,
	useRoute,
	RouteProp,
	NavigationProp,
} from "@react-navigation/native";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../styles/globalStyles";
// We no longer need the replaceWithNotFound helper
import {
	optimizeImageUrl,
	PRESETS,
} from "../utils/helpers/imageOptimization.helper";

// Import our custom hook for fetching club details data
import { useClubDetailsData } from "../hooks/useClubDetailsData";
import CompetitionPageSectionRenderer from "../components/sections/pageRenderers/CompetitionPageSectionRenderer";

// Define placeholder image at module level
const PLACEHOLDER_IMAGE = require("../assets/images/placeholder.jpg");

type ClubDetailsPageRouteProp = RouteProp<
	RootStackParamList,
	"ClubDetailsPage"
>;

/**
 * ClubDetailsPage Component
 *
 * This page displays club data including:
 * - Club information
 * - Inside videos
 * - Discovery videos
 * - Summary videos
 * - Replays
 */
const ClubDetailsPage = () => {
	// Navigation hook for screen transitions
	const navigation =
		useNavigation<NavigationProp<RootStackParamList>>();

	// Get route params to check for clubId and clubName
	const route = useRoute<ClubDetailsPageRouteProp>();
	const flatListRef = useRef<FlatList>(null);

	// Ensure clubId and clubName are provided from navigation params
	if (!route.params?.clubId || !route.params?.clubName) {
		console.error(
			"No clubId or clubName provided in navigation params"
		);
	}

	// Extract club information from route params
	const clubId = route.params?.clubId || "";
	const clubName = route.params?.clubName || "Club";
	const clubImage = route.params?.clubImage || "";
	const clubCodename = route.params?.clubCodename || "";

	// For backward compatibility, keep a simple fallback formatter
	// in case we can't find the club name in the API response
	const getSimpleClubName = (codename: string): string => {
		if (!codename || !codename.startsWith("club_")) return clubName;

		// Simple formatting: remove "club_" prefix, replace underscores with spaces, and capitalize
		return codename
			.replace("club_", "")
			.replace(/_/g, " ")
			.split(" ")
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(" ");
	};

	// Get a simple formatted club name as fallback
	const fallbackClubName = getSimpleClubName(clubCodename);

	// Use our custom hook to fetch all required data
	// Pass the clubCodename from navigation params to the hook
	const { pageData, sections, isLoading, error } = useClubDetailsData(
		clubId,
		clubCodename
	);

	// Find the hero section to extract the actual club name
	const heroSection = sections.find(
		(section) => section._kenticoItemType === "web_section___hero"
	);

	// Extract club name from hero section if available
	// The hero section has a 'name' property as shown in the API response:
	// "_kenticoItemType": "web_section___hero", "name": "Pays d'Aix Université Club"
	const clubNameFromAPI =
		heroSection &&
		"name" in heroSection &&
		typeof heroSection.name === "string"
			? heroSection.name
			: null;

	// Log the club name from API for debugging
	if (clubNameFromAPI) {
		console.log(
			`[ClubDetailsPage] Club name from API: "${clubNameFromAPI}"`
		);
	}

	// We no longer need to redirect to NotFoundPage for 404 errors
	// Instead, we'll display the error message directly in the ClubDetailsPage
	useEffect(() => {
		if (error) {
			console.log(`[ClubDetailsPage] Error: ${error}`);
		}
	}, [error]);

	// Image rendering with fallback
	const renderImage = (
		imageUrl: string | null | undefined,
		style?: StyleProp<ImageStyle>,
		resizeMode: "contain" | "cover" | "stretch" = "stretch"
	) => {
		// Optimize image URL if available
		const optimizedImageUrl = imageUrl
			? optimizeImageUrl(imageUrl, PRESETS.DEFAULT)
			: null;

		if (!optimizedImageUrl) {
			return (
				<Image
					source={PLACEHOLDER_IMAGE}
					style={[style]}
					resizeMode={resizeMode}
				/>
			);
		}

		return (
			<Image
				source={{ uri: optimizedImageUrl }}
				style={[style]}
				resizeMode={resizeMode}
				defaultSource={PLACEHOLDER_IMAGE}
				onError={() => {
					console.log(
						`[ClubDetailsPage] Image load failed for ${imageUrl}, using placeholder`
					);
				}}
			/>
		);
	};

	// Handle item press for videos and other content
	const handleItemPress = (
		id: string,
		type: string,
		eventData?: any
	) => {
		// Log item press for tracking
		console.log(`${type} item pressed: ${id}`, eventData);

		// Navigate to video details page
		if ((type === "video" || type === "event") && id) {
			// Determine if this is a live or upcoming event
			const isLive = eventData?.isLive || false;
			const startTime = eventData?.startDate || null;

			navigation.navigate("VideoDetailsPage", {
				video: {
					videoId: id,
					title: eventData?.name || "Video",
					thumbnail: eventData?.poster
						? String(eventData.poster)
						: "",
					description: eventData?.description || "",
					isLive: isLive,
					startTime: startTime,
				},
			});
		}
	};

	return (
		<View style={styles.container}>
			{isLoading ? (
				// Show loading indicator while data is being fetched
				<View style={styles.loadingContainer}>
					<ActivityIndicator
						size="large"
						color="#FFFFFF"
					/>
					<Text style={styles.loadingText}>
						Loading club details...
					</Text>
				</View>
			) : error ? (
				// Show error message if there's an error
				<View style={styles.errorContainer}>
					<Text style={styles.errorTitle}>Club Not Available</Text>
					<Text style={styles.errorText}>{error}</Text>
					<Pressable
						style={styles.backButton}
						onPress={() => navigation.goBack()}
					>
						<Text style={styles.backButtonText}>
							Return to Competition
						</Text>
					</Pressable>
				</View>
			) : (
				// Show club details when data is loaded
				<>
					<FlatList
						ref={flatListRef}
						data={sections}
						renderItem={({ item, index }) => (
							<View style={styles.sectionWrapper}>
								<CompetitionPageSectionRenderer
									key={`section-${item._kenticoId}-${index}`}
									section={item}
									upcomingEvents={[]}
									onItemPress={handleItemPress}
								/>
							</View>
						)}
						ListHeaderComponent={() => (
							<Pressable
								style={styles.headerBanner}
								onPress={() => {
									// Scroll to top when pressed
									if (flatListRef.current) {
										flatListRef.current.scrollToOffset({
											offset: 0,
											animated: true,
										});
									}
								}}
							>
								{renderImage(clubImage, styles.headerImage, "cover")}
								<View style={styles.headerOverlay}>
									<Text style={styles.headerTitle}>
										{clubNameFromAPI ||
											fallbackClubName ||
											pageData?.title ||
											clubName}
									</Text>
									{pageData?.description && (
										<Text style={styles.clubDescription}>
											{pageData.description}
										</Text>
									)}
								</View>
							</Pressable>
						)}
						ListEmptyComponent={() => (
							<View style={styles.emptyContainer}>
								<Text style={styles.emptyText}>
									No content available
								</Text>
							</View>
						)}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={styles.flatListContent}
						// Performance optimizations for TV platforms
						removeClippedSubviews={true}
						maxToRenderPerBatch={5}
						windowSize={5}
						initialNumToRender={5}
					/>
				</>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
	},
	loadingContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
	},
	loadingText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		marginTop: scale(40),
		fontSize: scale(36),
	},
	errorContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		padding: scale(40),
	},
	errorTitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: scale(10),
	},
	errorText: {
		color: GLOBAL_STYLES.COLORS.ACCENT,
		fontSize: scale(36),
		textAlign: "center",
		marginBottom: scale(40),
	},
	flatListContent: {
		paddingBottom: scale(100),
		paddingHorizontal: 0,
	},
	emptyContainer: {
		alignItems: "center",
		justifyContent: "center",
		padding: scale(100),
	},
	emptyText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
	},
	headerBanner: {
		width: "100%",
		height: scale(650),
		zIndex: 1,
		marginBottom: scale(30),
	},
	headerImage: {
		width: "100%",
		height: "100%",
		backgroundColor: "#000000",
	},
	headerOverlay: {
		position: "absolute",
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: "rgba(0, 0, 0, 0.3)",
		paddingHorizontal: scale(16),
		justifyContent: "flex-end",
	},
	headerTitle: {
		color: "#FFFFFF",
		fontSize: scale(64),
		fontWeight: "bold",
		marginBottom: scale(30),
	},
	clubDescription: {
		color: "#FFFFFF",
		fontSize: scale(32),
		marginBottom: scale(60),
		maxWidth: "60%",
	},
	backButton: {
		marginTop: scale(20),
		paddingVertical: scale(10),
		paddingHorizontal: scale(20),
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
	},
	backButtonText: {
		color: "#FFFFFF",
		fontSize: scale(16),
		fontWeight: "bold",
	},
	sectionWrapper: {
		paddingHorizontal: GLOBAL_STYLES.PAGE_HORIZONTAL_PADDING,
	},
});

export default ClubDetailsPage;
