// pages/VideoPlayer.tsx
import {
	StyleSheet,
	View,
	Text,
	ActivityIndicator,
	Platform,
	Pressable,
} from "react-native";
import React, {
	useEffect,
	useRef,
	useState,
	useCallback,
	memo,
	useMemo,
	useReducer,
} from "react";
import Video, { VideoRef, DRMType } from "react-native-video";

import {
	RouteProp,
	useRoute,
	useNavigation,
} from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";

import {
	mainAPIClient,
	setMainApiLanguage,
} from "../utils/mainApiInstance";
import { AuthService } from "../services/authService";
import { AccessDeniedScreen } from "../components/common";
import { useLanguage } from "../contexts/LanguageContext";

import { scale } from "../utils/helpers/dimensionScale.helper";
import AndroidTVCustomControls from "../components/AndroidTVCustomControls";
import useAndroidTVControls from "../hooks/useAndroidTVControls";

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

type VideoPlayerRouteProp = RouteProp<
	RootStackParamList,
	"VideoPlayer"
>;

interface VideoAccessResponse {
	url?: string | null;
	urlMp4?: string | null;
	visibility?: string;
	status?: string;
	awsStream?: "STANDBY" | "ONAIR" | "ARCHIVED";
}

interface EventAccessResponse {
	Video?: {
		url?: string;
		urlMp4?: string;
		id?: string;
	};
	Streams?: Array<{
		url: string;
		type: string;
		protocol: string;
	}>;
	startTime?: string;
	visibility?: string;
}

// ============================================================================
// STATE MANAGEMENT WITH useReducer
// ============================================================================

interface PlayerState {
	// Loading states
	isLoading: boolean;
	isBuffering: boolean;
	isVideoReady: boolean;
	isPlaybackStarted: boolean;

	// Data states
	videoAccess: VideoAccessResponse | null;
	streamUrl: string | null;

	// Error states
	error: string | null;
	errorType: "access_denied" | "general" | null;

	// Live stream states
	retryCount: number;
	countdown: number;
	customStartTime: string | null;
	showTimeoutError: boolean;
	timeoutMessage: string;

	// Android TV Custom Controls states
	showControls: boolean;
	paused: boolean;
	currentTime: number;
	duration: number;
	seeking: boolean;
}

type PlayerAction =
	| { type: "SET_LOADING"; payload: boolean }
	| { type: "SET_BUFFERING"; payload: boolean }
	| { type: "SET_VIDEO_READY"; payload: boolean }
	| { type: "SET_PLAYBACK_STARTED"; payload: boolean }
	| { type: "SET_VIDEO_ACCESS"; payload: VideoAccessResponse }
	| {
			type: "SET_ERROR";
			payload: { message: string; type: "access_denied" | "general" };
	  }
	| { type: "CLEAR_ERROR" }
	| { type: "INCREMENT_RETRY" }
	| {
			type: "SET_COUNTDOWN";
			payload: { countdown: number; startTime: string };
	  }
	| {
			type: "SET_TIMEOUT_ERROR";
			payload: { show: boolean; message: string };
	  }
	| { type: "RESET_STATE" }
	| { type: "TOGGLE_CONTROLS" }
	| { type: "SET_CONTROLS_VISIBLE"; payload: boolean }
	| { type: "SET_PAUSED"; payload: boolean }
	| { type: "SET_CURRENT_TIME"; payload: number }
	| { type: "SET_DURATION"; payload: number }
	| { type: "SET_SEEKING"; payload: boolean };

const initialPlayerState: PlayerState = {
	isLoading: true,
	isBuffering: false,
	isVideoReady: false,
	isPlaybackStarted: false,
	videoAccess: null,
	streamUrl: null,
	error: null,
	errorType: null,
	retryCount: 0,
	countdown: 0,
	customStartTime: null,
	showTimeoutError: false,
	timeoutMessage: "",
	showControls: true,
	paused: false,
	currentTime: 0,
	duration: 0,
	seeking: false,
};

function playerReducer(
	state: PlayerState,
	action: PlayerAction
): PlayerState {
	switch (action.type) {
		case "SET_LOADING":
			return { ...state, isLoading: action.payload };
		case "SET_BUFFERING":
			return { ...state, isBuffering: action.payload };
		case "SET_VIDEO_READY":
			return { ...state, isVideoReady: action.payload };
		case "SET_PLAYBACK_STARTED":
			return {
				...state,
				isPlaybackStarted: action.payload,
				isLoading: action.payload ? false : state.isLoading,
				isBuffering: action.payload ? false : state.isBuffering,
			};
		case "SET_VIDEO_ACCESS":
			return {
				...state,
				videoAccess: action.payload,
				streamUrl:
					action.payload.url || action.payload.urlMp4 || null,
				isLoading: false,
			};
		case "SET_ERROR":
			return {
				...state,
				error: action.payload.message,
				errorType: action.payload.type,
				isLoading: false,
				isBuffering: false,
			};
		case "CLEAR_ERROR":
			return { ...state, error: null, errorType: null };
		case "INCREMENT_RETRY":
			return { ...state, retryCount: state.retryCount + 1 };
		case "SET_COUNTDOWN":
			return {
				...state,
				countdown: action.payload.countdown,
				customStartTime: action.payload.startTime,
				isLoading: false,
			};
		case "SET_TIMEOUT_ERROR":
			return {
				...state,
				showTimeoutError: action.payload.show,
				timeoutMessage: action.payload.message,
			};
		case "RESET_STATE":
			return { ...initialPlayerState };
		case "TOGGLE_CONTROLS":
			return { ...state, showControls: !state.showControls };
		case "SET_CONTROLS_VISIBLE":
			return { ...state, showControls: action.payload };
		case "SET_PAUSED":
			return { ...state, paused: action.payload };
		case "SET_CURRENT_TIME":
			return { ...state, currentTime: action.payload };
		case "SET_DURATION":
			return { ...state, duration: action.payload };
		case "SET_SEEKING":
			return { ...state, seeking: action.payload };
		default:
			return state;
	}
}

// ============================================================================
// CONSTANTS
// ============================================================================

const MAX_RETRIES = 5;
const RETRY_DELAY = 1000;
const LIVE_STREAM_TIMEOUT = 10000;
const FAILSAFE_TIMEOUT = 5000;

const VIDEO_CONFIG = {
	RETRY_COUNT: 5,
} as const;

// ============================================================================
// MEMOIZED COMPONENTS
// ============================================================================

const LoadingState = memo(
	({
		isLive = false,
		retryCount,
	}: {
		isLive?: boolean;
		retryCount: number;
	}) => (
		<View style={styles.loadingContainer}>
			<ActivityIndicator
				size="large"
				color="#fff"
			/>
			{isLive && retryCount > 0 && (
				<Text style={styles.loadingText}>
					Connecting to live stream... ({retryCount}/{MAX_RETRIES})
				</Text>
			)}
		</View>
	)
);

const ErrorState = memo(
	({
		message,
		errorType,
	}: {
		message: string;
		errorType?: "access_denied" | "general";
	}) => {
		const navigation =
			useNavigation<NativeStackNavigationProp<RootStackParamList>>();
		const [isFocused, setIsFocused] = useState(false);

		const handleGoBack = useCallback(() => {
			navigation.goBack();
		}, [navigation]);

		if (errorType === "access_denied") {
			return <AccessDeniedScreen onCancel={handleGoBack} />;
		}

		return (
			<View style={styles.errorContainer}>
				<Text style={styles.error}>{message}</Text>
				<Pressable
					style={[
						styles.backButton,
						isFocused && styles.backButtonFocused,
					]}
					onPress={handleGoBack}
					onFocus={() => setIsFocused(true)}
					onBlur={() => setIsFocused(false)}
					hasTVPreferredFocus={true}
					focusable={true}
					accessible={true}
					accessibilityRole="button"
					accessibilityLabel="Go back to video details"
				>
					<Text
						style={[
							styles.backButtonText,
							isFocused && styles.backButtonTextFocused,
						]}
					>
						← Back
					</Text>
				</Pressable>
			</View>
		);
	}
);

const LiveIndicator = memo(() => (
	<View style={styles.liveIndicatorContainer}>
		<View style={styles.liveIndicator}>
			<Text style={styles.liveText}>LIVE</Text>
		</View>
	</View>
));

const UpcomingLiveStream = memo(
	({
		title,
		startTime,
	}: {
		title: string;
		startTime: string | undefined;
	}) => {
		const [timeLeft, setTimeLeft] = useState<string>("");

		useEffect(() => {
			if (!startTime) return;

			const calculateTimeLeft = () => {
				const targetDate = new Date(startTime).getTime();
				const now = Date.now();
				const difference = targetDate - now;

				if (difference <= 0) return "";

				const hours = Math.floor(difference / (1000 * 60 * 60));
				const minutes = Math.floor(
					(difference % (1000 * 60 * 60)) / (1000 * 60)
				);

				return `${hours}h ${minutes}m`;
			};

			setTimeLeft(calculateTimeLeft());

			const timer = setInterval(() => {
				setTimeLeft(calculateTimeLeft());
			}, 60000);

			return () => clearInterval(timer);
		}, [startTime]);

		return (
			<View style={styles.upcomingLiveContainer}>
				<Text style={styles.startTime}>
					Starts at:{" "}
					{startTime ? new Date(startTime).toLocaleDateString() : ""}
				</Text>
				{timeLeft && (
					<Text style={styles.timeLeft}>
						Stream starts in: {timeLeft}
					</Text>
				)}
			</View>
		);
	}
);

// ============================================================================
// CUSTOM HOOKS
// ============================================================================

const useVideoAccess = (videoId: string, isLive: boolean) => {
	const [state, dispatch] = useReducer(
		playerReducer,
		initialPlayerState
	);
	const abortControllerRef = useRef<AbortController | null>(null);
	const isMountedRef = useRef(true);
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	const delay = useCallback(
		(ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),
		[]
	);

	const determineErrorType = useCallback(
		async (error?: any): Promise<"access_denied" | "general"> => {
			try {
				const isAuthenticated = await AuthService.isAuthenticated();
				if (isAuthenticated) {
					if (
						error?.response?.status === 403 ||
						error?.response?.status === 401
					) {
						return "access_denied";
					}
					return "access_denied";
				} else {
					return "general";
				}
			} catch {
				return "general";
			}
		},
		[]
	);

	const handleNonAuthenticatedAccess = useCallback(async () => {
		try {
			const isAuthenticated = await AuthService.isAuthenticated();
			if (!isAuthenticated) {
				navigation.navigate("LoginPage");
				return true;
			}
			return false;
		} catch {
			return false;
		}
	}, [navigation]);

	const fetchVideoWithRetry = useCallback(async () => {
		if (!isMountedRef.current) return;

		abortControllerRef.current = new AbortController();
		const originalAuthHeader =
			mainAPIClient.instance.defaults.headers.common["Authorization"];

		try {
			dispatch({ type: "SET_LOADING", payload: true });

			// Try without authentication first
			delete mainAPIClient.instance.defaults.headers.common[
				"Authorization"
			];

			if (isLive) {
				try {
					const accessResponse =
						await mainAPIClient.platforms.getEventAccess(videoId, {
							signal: abortControllerRef.current.signal,
						});
					const eventData =
						accessResponse.data as EventAccessResponse;

					if (
						eventData.visibility === "public" &&
						eventData.Streams?.[0]?.url
					) {
						if (originalAuthHeader) {
							mainAPIClient.instance.defaults.headers.common[
								"Authorization"
							] = originalAuthHeader;
						}
						dispatch({
							type: "SET_VIDEO_ACCESS",
							payload: {
								url: eventData.Streams[0].url,
								urlMp4: undefined,
							},
						});
						return;
					} else if (eventData.startTime) {
						if (originalAuthHeader) {
							mainAPIClient.instance.defaults.headers.common[
								"Authorization"
							] = originalAuthHeader;
						}
						const targetDate = new Date(eventData.startTime);
						const now = new Date();
						const timeDifference =
							targetDate.getTime() - now.getTime();
						const hours = Math.floor(
							timeDifference / (1000 * 60 * 60)
						);
						const minutes = Math.floor(
							(timeDifference % (1000 * 60 * 60)) / (1000 * 60)
						);
						const formattedTime = `${String(hours).padStart(
							2,
							"0"
						)}:${String(minutes).padStart(2, "0")}`;
						const countdownDuration = Math.max(
							0,
							Math.floor(timeDifference / 1000)
						);
						dispatch({
							type: "SET_COUNTDOWN",
							payload: {
								countdown: countdownDuration,
								startTime: formattedTime,
							},
						});
						return;
					}
				} catch {
					// Continue to authenticated flow
				}
			} else {
				try {
					const accessResponse =
						await mainAPIClient.platforms.getVideoAccess(videoId, {
							signal: abortControllerRef.current.signal,
						});
					const accessData =
						accessResponse.data as VideoAccessResponse;

					if (
						accessData?.visibility === "public" &&
						(accessData.url || accessData.urlMp4)
					) {
						if (originalAuthHeader) {
							mainAPIClient.instance.defaults.headers.common[
								"Authorization"
							] = originalAuthHeader;
						}
						dispatch({
							type: "SET_VIDEO_ACCESS",
							payload: accessData,
						});
						return;
					}
				} catch {
					// Continue to authenticated flow
				}
			}

			// Try with authentication
			if (originalAuthHeader) {
				mainAPIClient.instance.defaults.headers.common[
					"Authorization"
				] = originalAuthHeader;
			}

			if (isLive) {
				const accessResponse =
					await mainAPIClient.platforms.getEventAccess(videoId, {
						signal: abortControllerRef.current.signal,
					});
				const eventData = accessResponse.data as EventAccessResponse;

				if (eventData.Streams?.[0]?.url) {
					dispatch({
						type: "SET_VIDEO_ACCESS",
						payload: {
							url: eventData.Streams[0].url,
							urlMp4: undefined,
						},
					});
					return;
				} else if (eventData.startTime) {
					const targetDate = new Date(eventData.startTime);
					const now = new Date();
					const timeDifference = targetDate.getTime() - now.getTime();
					const hours = Math.floor(timeDifference / (1000 * 60 * 60));
					const minutes = Math.floor(
						(timeDifference % (1000 * 60 * 60)) / (1000 * 60)
					);
					const formattedTime = `${String(hours).padStart(
						2,
						"0"
					)}:${String(minutes).padStart(2, "0")}`;
					const countdownDuration = Math.max(
						0,
						Math.floor(timeDifference / 1000)
					);
					dispatch({
						type: "SET_COUNTDOWN",
						payload: {
							countdown: countdownDuration,
							startTime: formattedTime,
						},
					});
					return;
				} else {
					if (eventData.visibility !== "public") {
						dispatch({
							type: "SET_ERROR",
							payload: {
								message: "You don't have access to this live event",
								type: "access_denied",
							},
						});
						return;
					}
				}

				const shouldNavigate = await handleNonAuthenticatedAccess();
				if (shouldNavigate) return;

				const errorTypeResult = await determineErrorType();
				dispatch({
					type: "SET_ERROR",
					payload: {
						message: "This video is not available",
						type: errorTypeResult,
					},
				});
				return;
			}

			const accessResponse =
				await mainAPIClient.platforms.getVideoAccess(videoId, {
					signal: abortControllerRef.current.signal,
				});
			const accessData = accessResponse.data as VideoAccessResponse;

			if (accessData && (accessData.url || accessData.urlMp4)) {
				dispatch({ type: "SET_VIDEO_ACCESS", payload: accessData });
				return;
			} else if (accessData?.visibility !== "public") {
				dispatch({
					type: "SET_ERROR",
					payload: {
						message: "Access denied",
						type: "access_denied",
					},
				});
				return;
			}

			const shouldNavigate = await handleNonAuthenticatedAccess();
			if (shouldNavigate) return;

			const errorTypeResult = await determineErrorType();
			dispatch({
				type: "SET_ERROR",
				payload: {
					message: "This video is not available",
					type: errorTypeResult,
				},
			});
		} catch (error: any) {
			if (originalAuthHeader) {
				mainAPIClient.instance.defaults.headers.common[
					"Authorization"
				] = originalAuthHeader;
			}

			if (error.name === "AbortError" || !isMountedRef.current) {
				dispatch({ type: "SET_LOADING", payload: false });
				return;
			}

			if (isLive && state.retryCount < MAX_RETRIES) {
				dispatch({ type: "INCREMENT_RETRY" });
				await delay(RETRY_DELAY);
				if (isMountedRef.current) {
					fetchVideoWithRetry();
				}
				return;
			}

			const shouldNavigate = await handleNonAuthenticatedAccess();
			if (shouldNavigate) return;

			const errorTypeResult = await determineErrorType(error);
			dispatch({
				type: "SET_ERROR",
				payload: {
					message: "This video is not available",
					type: errorTypeResult,
				},
			});
		}
	}, [
		videoId,
		isLive,
		state.retryCount,
		delay,
		determineErrorType,
		handleNonAuthenticatedAccess,
	]);

	useEffect(() => {
		dispatch({ type: "RESET_STATE" });
		fetchVideoWithRetry();

		return () => {
			isMountedRef.current = false;
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, [videoId]);

	return { state, dispatch, fetchVideoWithRetry };
};

const useLiveStreamTimeout = (
	isLive: boolean,
	isPlaybackStarted: boolean,
	navigation: any
) => {
	const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(
		null
	);

	const startTimeout = useCallback(() => {
		if (!isLive || isPlaybackStarted) return;

		const timeout = setTimeout(() => {
			navigation.navigate("HomePage");
		}, LIVE_STREAM_TIMEOUT);

		setTimeoutId(timeout);
	}, [isLive, isPlaybackStarted, navigation]);

	const clearTimeout = useCallback(() => {
		if (timeoutId) {
			global.clearTimeout(timeoutId);
			setTimeoutId(null);
		}
	}, [timeoutId]);

	useEffect(() => {
		if (isPlaybackStarted) {
			clearTimeout();
		}
	}, [isPlaybackStarted, clearTimeout]);

	return { startTimeout, clearTimeout };
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const VideoPlayer: React.FC = () => {
	const route = useRoute<VideoPlayerRouteProp>();
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();
	const { video } = route.params;
	const { currentLanguage } = useLanguage();
	const videoRef = useRef<VideoRef>(null);

	const { state, dispatch } = useVideoAccess(
		video.videoId,
		video.isLive || false
	);
	const { startTimeout, clearTimeout } = useLiveStreamTimeout(
		video.isLive || false,
		state.isPlaybackStarted,
		navigation
	);
	const androidTVControls = useAndroidTVControls({
		videoRef,
		initialPaused: state.paused,
		initialShowControls: state.showControls,
	});

	const {
		isAndroidTV,
		showControls,
		paused,
		currentTime,
		duration,
		seeking,
		focusedControl,
		onPlay,
		onPause,
		onProgress: onProgressAndroidTV,
		onLoad: onLoadAndroidTV,
		onSeek: onSeekAndroidTV,
		handlePlayPausePress,
		handleSeek,
		handleRewind,
		handleForward,
	} = androidTVControls;

	// Set language for main API requests
	useEffect(() => {
		setMainApiLanguage(currentLanguage);
	}, [currentLanguage]);

	// Failsafe timer to prevent stuck loading states
	useEffect(() => {
		if (state.streamUrl && state.isLoading) {
			const failsafeTimer = setTimeout(() => {
				dispatch({ type: "SET_LOADING", payload: false });
			}, FAILSAFE_TIMEOUT);

			return () => global.clearTimeout(failsafeTimer);
		}
	}, [state.streamUrl, state.isLoading]);

	// Memoized video source configuration
	const videoSource = useMemo(
		() => ({
			uri: state.streamUrl || "",
			headers: {
				"x-account-key": "ByAWCu-i5",
				"User-Agent": "HandballTV/Android",
				Origin: "https://www.handballtv.fr",
				Referer: "https://www.handballtv.fr/",
			},
			type: video.isLive ? "m3u8" : undefined,
		}),
		[state.streamUrl, video.isLive]
	);

	// Memoized DRM configuration
	const drmConfig = useMemo(
		() => ({
			type: Platform.OS === "android" ? DRMType.WIDEVINE : undefined,
			licenseServer: "https://widevine-license.onrewind.tv/proxy",
		}),
		[]
	);

	// Optimized event handlers
	const onBuffer = useCallback(
		({ isBuffering }: { isBuffering: boolean }) => {
			dispatch({ type: "SET_BUFFERING", payload: isBuffering });
		},
		[dispatch]
	);

	const onProgress = useCallback(
		(data: {
			currentTime: number;
			playableDuration: number;
			seekableDuration: number;
		}) => {
			if (isAndroidTV) {
				onProgressAndroidTV(data);
			} else {
				if (data.currentTime > 0.25 && !state.isPlaybackStarted) {
					dispatch({ type: "SET_PLAYBACK_STARTED", payload: true });
					clearTimeout();
				}
			}
		},
		[isAndroidTV, onProgressAndroidTV, state.isPlaybackStarted, clearTimeout, dispatch]
	);

	const onError = useCallback(
		(error: any) => {
			dispatch({
				type: "SET_ERROR",
				payload: { message: "Error playing video", type: "general" },
			});

			if (video.isLive) {
				dispatch({
					type: "SET_TIMEOUT_ERROR",
					payload: {
						show: true,
						message:
							"Live stream is not available. Returning to home page...",
					},
				});
				setTimeout(() => {
					navigation.navigate("HomePage");
				}, 2000);
			}
		},
		[video.isLive, navigation, dispatch]
	);

	const onLoad = useCallback(
		(data: any) => {
			clearTimeout();

			if (isAndroidTV) {
				onLoadAndroidTV(data);
			} else {
				dispatch({ type: "SET_VIDEO_READY", payload: true });
				dispatch({ type: "SET_LOADING", payload: false });

				setTimeout(() => {
					if (!state.isPlaybackStarted) {
						dispatch({ type: "SET_PLAYBACK_STARTED", payload: true });
					}
				}, 2000);
			}
		},
		[isAndroidTV, onLoadAndroidTV, state.isPlaybackStarted, clearTimeout, dispatch]
	);

	const onSeek = useCallback(
		(data: { currentTime: number }) => {
			if (isAndroidTV) {
				onSeekAndroidTV(data);
			}
		},
		[isAndroidTV, onSeekAndroidTV]
	);

	const onLoadStart = useCallback(() => {
		if (video.isLive) {
			startTimeout();
		}
	}, [video.isLive, startTimeout]);

	// Render logic
	if (state.error) {
		if (video.isLive && state.countdown > 0) {
			return (
				<UpcomingLiveStream
					title={video.title}
					startTime={state.customStartTime || ""}
				/>
			);
		}
		return (
			<ErrorState
				message={state.error}
				errorType={state.errorType || "general"}
			/>
		);
	}

	if (state.isLoading && !state.isPlaybackStarted) {
		return (
			<LoadingState
				isLive={video.isLive}
				retryCount={state.retryCount}
			/>
		);
	}

	return (
		<View style={styles.container}>
			{isAndroidTV ? (
				<Pressable
					style={styles.videoContainer}
					focusable={true}
					hasTVPreferredFocus={!showControls}
					onPress={handlePlayPausePress}
					onFocus={() => {
						console.log(
							"[VideoPlayer] Video container focused (Android TV)"
						);
					}}
					onBlur={() => {
						console.log(
							"[VideoPlayer] Video container blurred (Android TV)"
						);
					}}
				>
					{Boolean(video.isLive) && <LiveIndicator />}
					<Video
						ref={videoRef}
						source={videoSource}
						drm={drmConfig}
						minLoadRetryCount={VIDEO_CONFIG.RETRY_COUNT}
						controls={!isAndroidTV}
						paused={paused}
						onBuffer={onBuffer}
						onProgress={onProgress}
						onPlaybackStateChanged={(data) => {
							if (data.isPlaying) {
								onPlay();
							} else {
								onPause();
							}
						}}
						onSeek={onSeek}
						muted={false}
						style={styles.backgroundVideo}
						resizeMode={video.isLive ? "cover" : "contain"}
						repeat={!video.isLive}
						playInBackground={!video.isLive}
						playWhenInactive={!video.isLive}
						onLoadStart={onLoadStart}
						onLoad={onLoad}
						onError={onError}
					/>

					{!showControls && (
						<Pressable
							style={styles.invisibleOverlay}
							onPress={handlePlayPausePress}
							focusable={true}
							hasTVPreferredFocus={true}
							accessible={true}
							accessibilityRole="button"
							accessibilityLabel="Show video controls"
						/>
					)}

					<AndroidTVCustomControls
						visible={showControls}
						paused={paused}
						currentTime={currentTime}
						duration={duration}
						buffering={state.isBuffering}
						onPlayPause={handlePlayPausePress}
						onRewind={handleRewind}
						onForward={handleForward}
						onSeek={handleSeek}
						onFocusChange={(element) => {
							console.log(`[VideoPlayer] Control focused: ${element}`);
						}}
						focusedControl={focusedControl}
					/>

					{state.isBuffering && (
						<View style={styles.bufferingOverlay}>
							<ActivityIndicator
								size="large"
								color="#fff"
							/>
						</View>
					)}
					{state.showTimeoutError && (
						<View style={styles.timeoutErrorOverlay}>
							<Text style={styles.timeoutErrorText}>
								{state.timeoutMessage}
							</Text>
						</View>
					)}
				</Pressable>
			) : (
				<View style={styles.videoContainer}>
					{Boolean(video.isLive) && <LiveIndicator />}
					<Video
						ref={videoRef}
						source={videoSource}
						drm={drmConfig}
						minLoadRetryCount={VIDEO_CONFIG.RETRY_COUNT}
						controls={true}
						onBuffer={onBuffer}
						onProgress={onProgress}
						muted={false}
						style={styles.backgroundVideo}
						resizeMode={video.isLive ? "cover" : "contain"}
						repeat={!video.isLive}
						playInBackground={!video.isLive}
						playWhenInactive={!video.isLive}
						onLoadStart={onLoadStart}
						onLoad={onLoad}
						onError={onError}
					/>

					{state.isBuffering && (
						<View style={styles.bufferingOverlay}>
							<ActivityIndicator
								size="large"
								color="#fff"
							/>
						</View>
					)}
					{state.showTimeoutError && (
						<View style={styles.timeoutErrorOverlay}>
							<Text style={styles.timeoutErrorText}>
								{state.timeoutMessage}
							</Text>
						</View>
					)}
				</View>
			)}
		</View>
	);
};

export default memo(VideoPlayer);

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
	/**
	 * Core Layout Styles
	 * These styles define the main container and layout structure
	 * Uses flex layout for responsive video player positioning
	 */
	container: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#000",
	},
	loadingContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#000",
	},
	videoContainer: {
		width: "100%",
		height: "100%",
		position: "relative",
	},

	/**
	 * Video Player Styles
	 * Core styling for the video component
	 * Ensures proper video sizing and positioning
	 */
	backgroundVideo: {
		width: "100%",
		height: "100%",
	},

	/**
	 * Live Indicator Styles
	 * Visual elements for live stream status
	 * Positioned overlay with distinct styling for visibility
	 */
	liveIndicatorContainer: {
		position: "absolute",
		top: 16,
		left: 16,
		zIndex: 1,
	},
	liveIndicator: {
		backgroundColor: "#FF0000",
		paddingHorizontal: 8,
		paddingVertical: 4,
		borderRadius: 4,
	},
	liveText: {
		color: "#FFFFFF",
		fontWeight: "bold",
		fontSize: 12,
	},

	/**
	 * Status and Error Styles
	 * Text styles for various player states
	 * Includes loading and error message formatting
	 */
	errorContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#000",
		paddingHorizontal: 40,
	},
	error: {
		color: "#fff",
		fontSize: 18,
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: 30,
	},
	loadingText: {
		color: "#fff",
		marginTop: 20,
		fontSize: 16,
	},

	/**
	 * Back Button Styles for Error State
	 * Styles for the back button that appears in error states
	 * Includes focus states for tvOS navigation
	 * Always has border to prevent layout shifts - only color changes on focus
	 */
	backButton: {
		backgroundColor: "rgba(255, 255, 255, 0.1)",
		paddingHorizontal: 24,
		paddingVertical: 12,
		borderRadius: 8,
		borderWidth: 2,
		borderColor: "transparent",
		minWidth: 120,
	},
	backButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.2)",
		borderColor: "#fff",
	},
	backButtonText: {
		color: "#fff",
		fontSize: 16,
		fontWeight: "600",
		textAlign: "center",
	},
	backButtonTextFocused: {
		color: "#fff",
		fontWeight: "bold",
	},

	/**
	 * Buffering Overlay Styles
	 * Overlay for showing loading state during video buffering
	 */
	bufferingOverlay: {
		...StyleSheet.absoluteFillObject,
		backgroundColor: "rgba(0, 0, 0, 0.5)",
		justifyContent: "center",
		alignItems: "center",
	},

	/**
	 * Timeout Error Overlay Styles
	 * Overlay for showing timeout error messages for live streams
	 */
	timeoutErrorOverlay: {
		...StyleSheet.absoluteFillObject,
		backgroundColor: "rgba(0, 0, 0, 0.8)",
		justifyContent: "center",
		alignItems: "center",
		padding: scale(20),
	},
	timeoutErrorText: {
		color: "#fff",
		fontSize: scale(18),
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: scale(10),
	},
	countdownText: {
		color: "#fff",
		fontSize: 18,
		fontWeight: "bold",
		marginTop: 10,
	},
	upcomingLiveContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#000",
		paddingHorizontal: Platform.OS === "android" ? 10 : 20,
	},
	title: {
		color: "#fff",
		fontSize: Platform.OS === "android" ? 12 : 24,
		fontWeight: "bold",
		marginBottom: Platform.OS === "android" ? 5 : 10,
		textAlign: "center",
	},
	startTime: {
		color: "#fff",
		fontSize: Platform.OS === "android" ? 9 : 18,
		marginBottom: Platform.OS === "android" ? 2 : 5,
	},
	timeLeft: {
		color: "#fff",
		fontSize: Platform.OS === "android" ? 8 : 16,
	},

	/**
	 * Android TV Custom Controls Styles
	 * Styles for the custom video player controls overlay
	 * Designed for D-Pad navigation and TV viewing experience
	 */
	customControlsOverlay: {
		...StyleSheet.absoluteFillObject,
		backgroundColor: "rgba(0, 0, 0, 0.6)",
		justifyContent: "flex-end",
		paddingBottom: scale(40),
		paddingHorizontal: scale(40),
	},
	controlsContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		marginBottom: scale(20),
	},
	controlButton: {
		backgroundColor: "rgba(255, 255, 255, 0.2)",
		borderRadius: scale(25),
		width: scale(50),
		height: scale(50),
		justifyContent: "center",
		alignItems: "center",
		marginHorizontal: scale(10),
		borderWidth: 2,
		borderColor: "transparent",
	},
	controlButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.4)",
		borderColor: "#fff",
		transform: [{ scale: 1.1 }], // Slightly enlarge focused button
	},
	controlText: {
		color: "#fff",
		fontSize: scale(20),
		fontWeight: "bold",
	},
	playPauseButton: {
		backgroundColor: "rgba(255, 255, 255, 0.2)",
		borderRadius: scale(35),
		width: scale(70),
		height: scale(70),
		justifyContent: "center",
		alignItems: "center",
		marginHorizontal: scale(15),
		borderWidth: 2,
		borderColor: "transparent",
	},
	playPauseButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.4)",
		borderColor: "#fff",
		transform: [{ scale: 1.1 }],
	},
	playPauseText: {
		color: "#fff",
		fontSize: scale(28),
		fontWeight: "bold",
	},
	progressContainer: {
		flexDirection: "row",
		alignItems: "center",
		width: "100%",
	},
	timeText: {
		color: "#fff",
		fontSize: scale(14),
		fontWeight: "600",
		minWidth: scale(50),
		textAlign: "center",
	},
	seekBar: {
		flex: 1,
		height: scale(40),
		justifyContent: "center",
		marginHorizontal: scale(15),
		borderRadius: scale(4),
		borderWidth: 2,
		borderColor: "transparent",
	},
	seekBarFocused: {
		borderColor: "#fff",
		backgroundColor: "rgba(255, 255, 255, 0.1)",
	},
	seekBarTrack: {
		height: scale(6),
		backgroundColor: "rgba(255, 255, 255, 0.3)",
		borderRadius: scale(3),
		overflow: "hidden",
	},
	seekBarProgress: {
		height: "100%",
		backgroundColor: "#fff",
		borderRadius: scale(3),
	},

	/**
	 * Invisible Overlay for Android TV
	 * Captures events when controls are hidden
	 */
	invisibleOverlay: {
		...StyleSheet.absoluteFillObject,
		backgroundColor: "transparent",
	},
});
