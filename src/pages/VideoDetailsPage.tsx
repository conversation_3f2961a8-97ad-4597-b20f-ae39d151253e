// src/pages/VideoDetailsPage.tsx
// Related components:
// src/components/videoPlayerComponents/VideoDescription.tsx
// src/components/videoPlayerComponents/RelatedVideos.tsx
// src/components/videoPlayerComponents/VideoDetailsPlayButton.tsx
// src/components/videoPlayerComponents/UpcomingLiveModalTimer.tsx

import { StyleSheet, View, ImageBackground } from "react-native"; // Core React Native components and styling
import React, { memo } from "react"; // React library for building UI components
import { useRoute } from "@react-navigation/native"; // Hook to access route parameters
import { RootStackParamList } from "../app/index"; // Type definitions for navigation stack
import type { RouteProp } from "@react-navigation/native"; // Type for route properties
// components
import VideoDescription from "../components/videoDetailsComponents/VideoDescription"; // Component to display video description
import RelatedVideos from "../components/videoDetailsComponents/RelatedVideos"; // Component to display related videos
import VideoDetailsPlayButton from "../components/videoDetailsComponents/VideoDetailsPlayButton";
import UpcomingLiveModalTimer from "../components/videoDetailsComponents/UpcomingLiveModalTimer";
import { scale } from "../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../styles/globalStyles";

type VideoPlayerRouteProp = RouteProp<
	RootStackParamList,
	"VideoPlayer"
>;

// Define placeholder image at module level
const PLACEHOLDER_IMAGE = require("../assets/images/placeholder.jpg");

// Component to handle conditional rendering based on video type
const VideoDetailsContent = ({ video }: { video: any }) => {
	// Calculate time difference for upcoming videos
	const isUpcoming = video.startTime
		? new Date(video.startTime).getTime() - new Date().getTime() > 0
		: false;

	return (
		<View style={styles.contentContainer}>
			<View style={styles.mainContent}>
				<View style={styles.playButtonSection}>
					{isUpcoming ? (
						<UpcomingLiveModalTimer
							startTime={video.startTime}
							title={video.title}
						/>
					) : (
						<VideoDetailsPlayButton video={video} />
					)}
				</View>
			</View>
			<View style={styles.bottomContent}>
				<View style={styles.videoDescription}>
					<VideoDescription video={video} />
				</View>
				<View style={styles.relatedVideosContainer}>
					<RelatedVideos
						videoId={video.videoId}
						isUpcomingEvent={isUpcoming}
					/>
				</View>
			</View>
		</View>
	);
};

const VideoDetailsPage = () => {
	const route = useRoute<VideoPlayerRouteProp>();
	const { video } = route.params;

	// Handle empty or invalid thumbnail URLs
	const backgroundSource =
		video.thumbnail && video.thumbnail !== "undefined"
			? { uri: video.thumbnail }
			: PLACEHOLDER_IMAGE;

	return (
		<ImageBackground
			source={backgroundSource}
			style={styles.pageContainer}
			blurRadius={3}
			defaultSource={PLACEHOLDER_IMAGE} // Fallback while loading
			onError={() => {
				console.log(
					"[VideoDetailsPage] Background image load failed, using placeholder"
				);
			}}
		>
			<View style={styles.overlay}>
				<VideoDetailsContent video={video} />
			</View>
		</ImageBackground>
	);
};

export default memo(VideoDetailsPage);

const styles = StyleSheet.create({
	pageContainer: {
		flex: 1,
		width: "100%",
		height: "100%",
	},
	overlay: {
		flex: 1,
		backgroundColor: "rgba(10, 25, 47, 0.85)", // Keep this custom overlay color
		justifyContent: "flex-end",
	},
	contentContainer: {
		flex: 1,
		flexDirection: "column",
		justifyContent: "space-between",
		padding: scale(40),
		paddingBottom: scale(80),
	},
	mainContent: {
		flex: 1,
		justifyContent: "flex-end",
		marginBottom: scale(40),
	},
	bottomContent: {
		marginTop: "auto",
		marginBottom: scale(-80),
	},
	playButtonSection: {
		alignItems: "center",
	},
	videoDescription: {
		height: scale(240),
		marginBottom: scale(30), // Increased margin for better separation
		overflow: "hidden", // Ensure content stays within bounds
	},
	relatedVideosContainer: {
		height: scale(312),
	},
});
