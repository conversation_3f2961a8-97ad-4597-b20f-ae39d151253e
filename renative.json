{"$schema": ".rnv/schema/rnv.project.json", "extendsTemplate": "@rnv/template-starter/renative.json", "projectName": "OTT_TV_POC", "workspaceID": "rnv", "templateConfig": {"name": "@rnv/template-starter", "version": "1.9.1"}, "defaults": {"supportedPlatforms": ["web", "ios", "android", "androidtv", "firetv", "tvos", "macos", "linux", "windows", "tizen", "webos"]}, "platforms": {"tvos": {"templateXcode": {"Info_plist": {"ITSAppUsesNonExemptEncryption": "NO"}, "project_pbxproj": {"resourceFiles": ["RNVApp/Assets.xcassets"]}}, "assetSources": []}, "webos": {"assetSources": []}, "androidtv": {"assetSources": []}}, "plugins": {"react-native-safe-area-context": "source:rnv", "@react-navigation/native": "source:rnv", "@react-navigation/native-stack": "5.0.5", "react-native-screens": "source:rnv", "react-native-video": "source:rnv", "axios": "source:rnv", "react-native-linear-gradient": {"version": "latest", "tvos": {"enabled": true, "podName": "BVLinearGradient"}}}}