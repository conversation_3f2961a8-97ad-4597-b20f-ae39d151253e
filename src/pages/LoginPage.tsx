import React, { useState, memo, useCallback } from "react";
import {
	View,
	Text,
	TextInput,
	StyleSheet,
	ActivityIndicator,
	Platform,
	Pressable,
} from "react-native";
import { GLOBAL_STYLES } from "../styles/globalStyles";
import {
	useFocusEffect,
	useNavigation,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";
import {
	AuthService,
	LoginCredentials,
} from "../services/authService";
import { QRCodeSection } from "../components/common";
import { useLanguage } from "../contexts/LanguageContext";
import { getTranslation } from "../utils/translations";

/**
 * LoginPage Component
 * Displays login form alongside QR code for account management
 * Optimized with React.memo for performance
 * Enhanced with TV-specific focus management and keyboard handling
 */
const LoginPage = memo(() => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [currentFocus, setCurrentFocus] = useState<
		"email" | "password" | "button" | null
	>(null);
	const [showKeyboard, setShowKeyboard] = useState(false);
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	// Get current language for translations
	const { currentLanguage } = useLanguage();

	// References for input fields and button to manage focus
	const emailInputRef = React.useRef<TextInput>(null);
	const passwordInputRef = React.useRef<TextInput>(null);
	const loginButtonRef = React.useRef<any>(null);

	// Refs to hold the latest state values to prevent race conditions
	const emailStateRef = React.useRef(email);
	emailStateRef.current = email;
	const passwordStateRef = React.useRef(password);
	passwordStateRef.current = password;

	// Platform detection for different TV implementations
	const isAndroidTV = Platform.isTV && Platform.OS === "android";
	const isTvOS = Platform.isTV && Platform.OS === "ios";

	// Reset focus state when the screen comes into focus
	useFocusEffect(
		useCallback(() => {
			console.log("🔄 LoginPage focused - resetting focus state");
			setCurrentFocus(null);

			// Ensure all elements are blurred to prevent stale focus styling
			setTimeout(() => {
				emailInputRef.current?.blur();
				passwordInputRef.current?.blur();
				if (loginButtonRef.current?.blur) {
					loginButtonRef.current.blur();
				}
			}, 100); // Small delay to ensure refs are available

			// Cleanup function to reset focus when component unmounts or loses focus
			return () => {
				console.log(
					"🧹 LoginPage unfocused/unmounted - cleaning up focus state"
				);
				setCurrentFocus(null);
			};
		}, [])
	);

	// Memoized handlers for performance
	const handleEmailChange = useCallback(
		(text: string) => {
			// Trim whitespace from email input
			const trimmedEmail = text.trim();
			setEmail(trimmedEmail);
			if (error) setError("");
		},
		[error]
	);

	const handlePasswordChange = useCallback(
		(text: string) => {
			setPassword(text);
			if (error) setError("");
		},
		[error]
	);

	// Handle input field selection (Android TV specific)
	const handleInputSelect = useCallback(
		(inputType: "email" | "password") => {
			if (isAndroidTV) {
				setShowKeyboard(true);
				if (inputType === "email") {
					emailInputRef.current?.focus();
				} else {
					passwordInputRef.current?.focus();
				}
			}
		},
		[isAndroidTV]
	);

	/**
	 * Handle login form submission
	 * Validates input and calls authentication service
	 */
	const handleLogin = useCallback(async () => {
		setError("");

		const email = emailStateRef.current;
		const password = passwordStateRef.current;

		// Only check if fields are not empty, but don't block if they have content
		if (!email.trim() || !password.trim()) {
			console.log("Login attempted with empty fields:", {
				hasEmail: !!email.trim(),
				hasPassword: !!password.trim(),
			});
			return;
		}

		setIsLoading(true);

		try {
			console.log("🔐 Starting login attempt for:", email);
			const credentials: LoginCredentials = {
				email: email.trim(),
				password: password,
			};
			const result = await AuthService.login(credentials);

			if (result.success) {
				console.log(
					"✅ Login successful! Navigating back to HomePage"
				);
				console.log(
					"🏠 HomePage will re-check authentication status on focus"
				);
				// Clear focus state and blur all inputs before navigating away to prevent stale styling
				setCurrentFocus(null);
				emailInputRef.current?.blur();
				passwordInputRef.current?.blur();
				if (loginButtonRef.current?.blur) {
					loginButtonRef.current.blur();
				}
				navigation.goBack();
			} else {
				console.log("❌ Login failed:", result.error);
				setError(
					result.error ||
						getTranslation("loginFailed", currentLanguage)
				);
			}
		} catch (error) {
			console.log("💥 Unexpected login error:", error);
			setError(getTranslation("unexpectedError", currentLanguage));
		} finally {
			setIsLoading(false);
		}
	}, [currentLanguage, navigation]);

	// tvOS-specific navigation handlers
	const handleEmailSubmit = useCallback(() => {
		if (isTvOS) {
			console.log(
				"📱 tvOS Email submit - moving focus to password field"
			);
			// Move focus to password field when "Next" is pressed on email field
			passwordInputRef.current?.focus();
		}
	}, [isTvOS]);

	const handlePasswordSubmit = useCallback(() => {
		if (isTvOS) {
			console.log("📱 tvOS Password submit - attempting login");
			// Add a small delay to ensure state is updated
			setTimeout(() => {
				const email = emailStateRef.current;
				const password = passwordStateRef.current;

				// Only attempt login if both fields have content
				if (email.trim() && password.trim()) {
					handleLogin();
				}
			}, 100);
		}
	}, [isTvOS, handleLogin]);

	return (
		<View style={styles.pageBackground}>
			<View style={styles.mainContainer}>
				{/* Left side - Login Form */}
				<View style={styles.loginSection}>
					<View style={styles.card}>
						<Text style={styles.title}>
							{getTranslation("login", currentLanguage)}
						</Text>

						{/* Error message display */}
						{error ? (
							<View style={styles.errorContainer}>
								<Text style={styles.errorText}>{error}</Text>
							</View>
						) : null}

						<View style={styles.formGroup}>
							<Text style={styles.label}>
								{getTranslation("email", currentLanguage)}
							</Text>
							{isAndroidTV ? (
								// Android TV implementation with Pressable wrapper
								<Pressable
									style={[
										styles.inputWrapper,
										currentFocus === "email" &&
											styles.inputWrapperFocused,
									]}
									onPress={() => handleInputSelect("email")}
									onFocus={() => setCurrentFocus("email")}
									onBlur={() => {
										if (!showKeyboard) {
											emailInputRef.current?.blur();
										}
									}}
									focusable={true}
									hasTVPreferredFocus={true}
								>
									<TextInput
										ref={emailInputRef}
										style={[
											styles.input,
											error ? styles.inputError : null,
										]}
										value={email}
										onChangeText={handleEmailChange}
										placeholder={getTranslation(
											"enterEmail",
											currentLanguage
										)}
										placeholderTextColor={
											GLOBAL_STYLES.COLORS.TEXT_TERTIARY
										}
										autoCapitalize="none"
										keyboardType="email-address"
										editable={!isLoading}
										accessible={true}
										accessibilityLabel="Email input field"
										accessibilityRole="keyboardkey"
										onBlur={() => {
											if (!showKeyboard) {
												setShowKeyboard(false);
											}
										}}
									/>
								</Pressable>
							) : (
								// tvOS implementation with direct TextInput and proper keyboard navigation
								<TextInput
									ref={emailInputRef}
									style={[
										styles.input,
										error ? styles.inputError : null,
									]}
									value={email}
									onChangeText={handleEmailChange}
									placeholder={getTranslation(
										"enterEmail",
										currentLanguage
									)}
									placeholderTextColor={
										GLOBAL_STYLES.COLORS.TEXT_TERTIARY
									}
									autoCapitalize="none"
									keyboardType="email-address"
									editable={!isLoading}
									returnKeyType="next"
									onSubmitEditing={handleEmailSubmit}
									accessible={true}
									accessibilityLabel="Email input field"
									// Enhanced tvOS text handling
									autoCorrect={false}
									spellCheck={false}
									textContentType="emailAddress"
									// Ensure proper focus behavior on tvOS
									onFocus={() => {
										console.log("📱 Email input focused on tvOS");
										setCurrentFocus("email");
									}}
									onBlur={() => {
										console.log("📱 Email input blurred on tvOS");
										setCurrentFocus(null);
									}}
								/>
							)}
						</View>

						<View style={styles.formGroup}>
							<Text style={styles.label}>
								{getTranslation("password", currentLanguage)}
							</Text>
							{isAndroidTV ? (
								// Android TV implementation with Pressable wrapper
								<Pressable
									style={[
										styles.inputWrapper,
										currentFocus === "password" &&
											styles.inputWrapperFocused,
									]}
									onPress={() => handleInputSelect("password")}
									onFocus={() => setCurrentFocus("password")}
									onBlur={() => {
										if (!showKeyboard) {
											passwordInputRef.current?.blur();
										}
									}}
									focusable={true}
								>
									<TextInput
										ref={passwordInputRef}
										style={[
											styles.input,
											error ? styles.inputError : null,
										]}
										value={password}
										onChangeText={handlePasswordChange}
										placeholder={getTranslation(
											"enterPassword",
											currentLanguage
										)}
										placeholderTextColor={
											GLOBAL_STYLES.COLORS.TEXT_TERTIARY
										}
										secureTextEntry
										editable={!isLoading}
										accessible={true}
										accessibilityLabel="Password input field"
										accessibilityRole="keyboardkey"
										onBlur={() => {
											if (!showKeyboard) {
												setShowKeyboard(false);
											}
										}}
									/>
								</Pressable>
							) : (
								// tvOS implementation with direct TextInput and proper keyboard navigation
								<TextInput
									ref={passwordInputRef}
									style={[
										styles.input,
										error ? styles.inputError : null,
									]}
									value={password}
									onChangeText={handlePasswordChange}
									placeholder={getTranslation(
										"enterPassword",
										currentLanguage
									)}
									placeholderTextColor={
										GLOBAL_STYLES.COLORS.TEXT_TERTIARY
									}
									secureTextEntry
									editable={!isLoading}
									returnKeyType="done"
									onSubmitEditing={handlePasswordSubmit}
									accessible={true}
									accessibilityLabel="Password input field"
									// Enhanced tvOS text handling
									autoCorrect={false}
									spellCheck={false}
									textContentType="password"
									// Ensure proper focus behavior on tvOS
									onFocus={() => {
										console.log("📱 Password input focused on tvOS");
										setCurrentFocus("password");
									}}
									onBlur={() => {
										console.log("📱 Password input blurred on tvOS");
										setCurrentFocus(null);
									}}
								/>
							)}
						</View>

						<Pressable
							ref={loginButtonRef}
							style={({ pressed }) => [
								styles.button,
								isLoading && styles.buttonDisabled,
								(pressed || currentFocus === "button") &&
									styles.buttonFocused,
							]}
							onPress={handleLogin}
							disabled={isLoading}
							onFocus={() => {
								console.log("🎯 Login button focused");
								setCurrentFocus("button");
							}}
							onBlur={() => {
								console.log("🎯 Login button blurred");
								setCurrentFocus(null);
							}}
							focusable={true}
							accessible={true}
							accessibilityRole="button"
							accessibilityLabel="Log in button"
						>
							{isLoading ? (
								<View style={styles.buttonContent}>
									<ActivityIndicator
										size="small"
										color={GLOBAL_STYLES.COLORS.TEXT_PRIMARY}
										style={styles.loadingIndicator}
									/>
									<Text style={styles.buttonText}>
										{getTranslation("loggingIn", currentLanguage)}
									</Text>
								</View>
							) : (
								<Text style={styles.buttonText}>
									{getTranslation("logIn", currentLanguage)}
								</Text>
							)}
						</Pressable>
					</View>
				</View>

				{/* Right side - QR Code Section */}
				<View style={styles.qrSection}>
					<QRCodeSection
						title={getTranslation("manageAccountQR", currentLanguage)}
						showTitle={true}
					/>
				</View>
			</View>
		</View>
	);
});

const styles = StyleSheet.create({
	pageBackground: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
	},
	mainContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
		maxWidth: scale(1600),
		paddingHorizontal: scale(64),
	},
	loginSection: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		paddingRight: scale(80),
	},
	qrSection: {
		flex: 1,
		alignItems: "flex-start",
		justifyContent: "center",
		paddingLeft: scale(80),
	},
	card: {
		backgroundColor: "#131c2b",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(80),
		minWidth: scale(700),
		width: "100%",
		maxWidth: scale(800),
		alignItems: "center",
		// Removed shadow effects for cleaner appearance
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(64),
		fontWeight: "bold",
		marginBottom: scale(64),
		letterSpacing: scale(2),
	},
	formGroup: {
		width: "100%",
		marginBottom: scale(48),
	},
	label: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(32),
		marginBottom: scale(16),
		marginLeft: scale(2),
	},
	inputWrapper: {
		width: "100%",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		borderWidth: scale(3), // Use consistent border width to prevent layout shifts
		borderColor: "#22304a", // Default border color
		backgroundColor: "#18243a",
	},
	inputWrapperFocused: {
		borderColor: "#ffffff", // White border when focused - only color changes, not width
		// Border width stays the same to prevent visual artifacts and layout shifts
	},
	input: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		paddingVertical: scale(28),
		paddingHorizontal: scale(32),
		fontSize: scale(32),
		width: "100%",
		backgroundColor:
			Platform.isTV && Platform.OS === "ios"
				? "#18243a"
				: "transparent",
		borderRadius:
			Platform.isTV && Platform.OS === "ios"
				? GLOBAL_STYLES.BORDER_RADIUS
				: 0,
		borderWidth:
			Platform.isTV && Platform.OS === "ios" ? scale(2) : 0,
		borderColor:
			Platform.isTV && Platform.OS === "ios"
				? "#22304a"
				: "transparent",
	},
	button: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(28),
		paddingHorizontal: 0,
		marginTop: scale(32),
		width: "100%",
		alignItems: "center",
		borderWidth: scale(3), // Add consistent border width to prevent layout shifts
		borderColor: "transparent", // Transparent border by default
	},
	buttonFocused: {
		borderColor: "#ffffff", // White border when focused - only color changes, not width
		// Border width stays the same to prevent visual artifacts and layout shifts
	},
	buttonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		letterSpacing: scale(1),
	},
	buttonDisabled: {
		// Removed opacity - use color changes instead for disabled state
	},
	buttonContent: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
	},
	loadingIndicator: {
		marginRight: scale(12),
	},
	errorContainer: {
		backgroundColor: "#ff4444",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(16),
		marginBottom: scale(24),
		width: "100%",
	},
	errorText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		textAlign: "center",
	},
	inputError: {
		borderColor: "#ff4444",
	},
});

// Set display name for debugging
LoginPage.displayName = "LoginPage";

export default LoginPage;
