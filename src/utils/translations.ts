import { SupportedLanguage } from "../contexts/LanguageContext";

// Translation keys for type safety
export type TranslationKey =
	| "today"
	| "tomorrow"
	| "days"
	| "hours"
	| "minutes"
	| "monday"
	| "tuesday"
	| "wednesday"
	| "thursday"
	| "friday"
	| "saturday"
	| "sunday"
	| "login"
	| "email"
	| "password"
	| "enterEmail"
	| "enterPassword"
	| "logIn"
	| "loggingIn"
	| "emailRequired"
	| "validEmailRequired"
	| "passwordRequired"
	| "passwordMinLength"
	| "loginFailed"
	| "unexpectedError"
	| "manageAccountQR"
	| "accessDeniedTitle"
	| "accessDeniedSubtitle"
	| "cancel"
	| "accessDeniedError";

// Translation dictionaries
const translations = {
	en: {
		today: "Today",
		tomorrow: "Tomorrow",
		days: "DAYS",
		hours: "HOURS",
		minutes: "MINUTES",
		monday: "Monday",
		tuesday: "Tuesday",
		wednesday: "Wednesday",
		thursday: "Thursday",
		friday: "Friday",
		saturday: "Saturday",
		sunday: "Sunday",
		login: "LOG IN",
		email: "Email",
		password: "Password",
		enterEmail: "Enter your email",
		enterPassword: "Enter your password",
		logIn: "Log in",
		loggingIn: "Logging in...",
		emailRequired: "Email is required",
		validEmailRequired: "Please enter a valid email address",
		passwordRequired: "Password is required",
		passwordMinLength: "Password must be at least 1 characters long",
		loginFailed: "Login failed. Please try again.",
		unexpectedError:
			"An unexpected error occurred. Please try again.",
		manageAccountQR:
			"TO MANAGE ACCOUNT AND SUBSCRIPTION PLEASE FOLLOW THE QR CODE",
		accessDeniedTitle: "YOU DON'T HAVE ACCESS TO THIS VIDEO.",
		accessDeniedSubtitle:
			"TO MANAGE THE SUBSCRIPTION PLEASE FOLLOW THE QR CODE",
		cancel: "CANCEL",
		accessDeniedError: "You don't have access to this video",
	},
	fr: {
		today: "Aujourd'hui",
		tomorrow: "Demain",
		days: "JOURS",
		hours: "HEURES",
		minutes: "MINUTES",
		monday: "Lundi",
		tuesday: "Mardi",
		wednesday: "Mercredi",
		thursday: "Jeudi",
		friday: "Vendredi",
		saturday: "Samedi",
		sunday: "Dimanche",
		login: "CONNEXION",
		email: "E-mail",
		password: "Mot de passe",
		enterEmail: "Entrez votre e-mail",
		enterPassword: "Entrez votre mot de passe",
		logIn: "Se connecter",
		loggingIn: "Connexion en cours...",
		emailRequired: "L'e-mail est requis",
		validEmailRequired: "Veuillez entrer une adresse e-mail valide",
		passwordRequired: "Le mot de passe est requis",
		passwordMinLength:
			"Le mot de passe doit contenir au moins 1 caractère",
		loginFailed: "Échec de la connexion. Veuillez réessayer.",
		unexpectedError:
			"Une erreur inattendue s'est produite. Veuillez réessayer.",
		manageAccountQR:
			"POUR GÉRER LE COMPTE ET L'ABONNEMENT, VEUILLEZ SUIVRE LE CODE QR",
		accessDeniedTitle: "VOUS N'AVEZ PAS ACCÈS À CETTE VIDÉO.",
		accessDeniedSubtitle:
			"POUR GÉRER L'ABONNEMENT, VEUILLEZ SUIVRE LE CODE QR",
		cancel: "ANNULER",
		accessDeniedError: "Vous n'avez pas accès à cette vidéo",
	},
} as const;

/**
 * Get translated text for a given key and language
 * @param key - Translation key
 * @param language - Target language
 * @returns Translated text
 */
export const getTranslation = (
	key: TranslationKey,
	language: SupportedLanguage
): string => {
	return translations[language][key];
};

/**
 * Format date with proper localization
 * Returns "Today", "Tomorrow", or formatted date based on language
 * @param date - Date to format
 * @param language - Target language
 * @returns Formatted date string
 */
export const formatLocalizedDate = (
	date: Date,
	language: SupportedLanguage
): string => {
	const now = new Date();
	const tomorrow = new Date(now);
	tomorrow.setDate(tomorrow.getDate() + 1);

	// Check if it's today
	if (date.toDateString() === now.toDateString()) {
		return getTranslation("today", language);
	}

	// Check if it's tomorrow
	if (date.toDateString() === tomorrow.toDateString()) {
		return getTranslation("tomorrow", language);
	}

	// For other dates, format as "Wednesday 7" (day of week + day number)
	const weekdayKey = getWeekdayKey(date.getDay());
	const weekdayName = getTranslation(weekdayKey, language);
	const dayNumber = date.getDate();

	return `${weekdayName} ${dayNumber}`;
};

/**
 * Get weekday translation key from day number
 * @param dayNumber - Day number (0 = Sunday, 1 = Monday, etc.)
 * @returns Translation key for the weekday
 */
const getWeekdayKey = (dayNumber: number): TranslationKey => {
	const weekdays: TranslationKey[] = [
		"sunday", // 0
		"monday", // 1
		"tuesday", // 2
		"wednesday", // 3
		"thursday", // 4
		"friday", // 5
		"saturday", // 6
	];
	return weekdays[dayNumber];
};

/**
 * Format time with proper localization
 * @param date - Date to format time from
 * @param language - Target language
 * @returns Formatted time string (24-hour format)
 */
export const formatLocalizedTime = (
	date: Date,
	language: SupportedLanguage
): string => {
	// Use 24-hour format for both languages
	const hours = date.getHours();
	const minutes = date.getMinutes();
	return `${hours}:${minutes < 10 ? "0" + minutes : minutes}`;
};

/**
 * Format date and time together
 * @param dateString - ISO date string
 * @param language - Target language
 * @returns Object with formatted date text and time
 */
export const formatEventDateTime = (
	dateString: string | null | undefined,
	language: SupportedLanguage
): { text: string; time: string } => {
	// default text and time placeholders
	if (!dateString) {
		return {
			text: getTranslation("tomorrow", language),
			time: "15:55",
		};
	}

	const date = new Date(dateString);
	const text = formatLocalizedDate(date, language);
	const time = formatLocalizedTime(date, language);

	return { text, time };
};
