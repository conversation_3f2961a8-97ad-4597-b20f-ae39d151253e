import React from "react";
import { View, FlatList, StyleSheet, Text } from "react-native";
import {
	BaseSectionRendererProps,
	SectionWithItems,
} from "../../../types/sectionTypes";
import {
	LiveCarouselDynamicSection,
	OriginsEvent,
} from "../../../utils/apis/generated/kentico";
import LiveVideoCard from "../../common/LiveVideoCard";
import { scale } from "../../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../../utils/helpers/imageOptimization.helper";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";
import { useLanguage } from "../../../contexts/LanguageContext";
import {
	formatLocalizedDate,
	formatLocalizedTime,
} from "../../../utils/translations";
import { validateLiveEventStatus } from "../../../utils/kenticoHelpers";

// Define constants for item dimensions (matching homepage implementation)
const COMING_LIVE_ITEM_WIDTH = scale(432);
const COMING_LIVE_ITEM_HEIGHT = scale(244);

/**
 * Props for the Live Section Renderer
 */
export interface LiveSectionRendererProps
	extends BaseSectionRendererProps<LiveCarouselDynamicSection> {
	// Optional props for upcoming events that can be passed from parent
	upcomingEvents?: OriginsEvent[];
}

/**
 * LiveSectionRenderer Component
 *
 * Specialized renderer for live match sections
 * Displays live matches with time information
 * Shows upcoming matches, with live matches appearing first if available
 */
const LiveSectionRenderer: React.FC<LiveSectionRendererProps> = ({
	data,
	onPress,
	accessibilityLabel,
	upcomingEvents = [], // Default to empty array if not provided
}) => {
	// Get current language from context
	const { currentLanguage } = useLanguage();

	// Cast data to SectionWithItems to ensure TypeScript knows it has an items property
	const sectionData = data as SectionWithItems;

	// Use section items as live events
	const liveItems = sectionData.items || [];

	// Log basic section info
	console.log(
		"LiveSectionRenderer - Section Title:",
		sectionData.title,
		"Codename:",
		sectionData._kenticoCodename
	);

	// Filter out expired and inactive events using enhanced validation
	const filterExpiredEvents = (
		items: any[],
		isLiveItems: boolean = false
	) => {
		return items.filter((item: any) => {
			// For live items, use the comprehensive validation function
			if (isLiveItems) {
				const isValid = validateLiveEventStatus(item);
				if (!isValid) {
					console.log(
						`Filtering out invalid live event: ${
							item.event?.name || item.name || item.title || "unknown"
						}`
					);
				}
				return isValid;
			}

			// For upcoming events, use basic timestamp validation
			const now = new Date();
			let startDate: Date | null = null;

			if (item.event?.startDate) {
				startDate = new Date(item.event.startDate);
			} else if (item.startDate) {
				startDate = new Date(item.startDate);
			}

			// If no start date is available, keep the item (better to show than hide)
			if (!startDate || isNaN(startDate.getTime())) {
				console.warn(
					"Event has invalid or missing start date:",
					item
				);
				return true;
			}

			// For upcoming events, only filter if they're in the past
			const isExpired = startDate < now;

			if (isExpired) {
				console.log(
					`Filtering out expired upcoming event: ${
						item.event?.name || item.name || item.title
					} (started: ${startDate.toISOString()})`
				);
			}

			return !isExpired;
		});
	};

	// Map items to a format that can be used by the FlatList
	const mapItemsForDisplay = (
		sourceItems: any[],
		isLiveItem = true
	) => {
		// First filter out expired events, passing whether these are live items
		const filteredItems = filterExpiredEvents(
			sourceItems,
			isLiveItem
		);

		return filteredItems.map((item: any) => {
			// Process each item
			// Extract data from the item
			let id: string = "";
			let title: string = "";
			let imageUrl: string = "";
			let itemType: string = "";
			let eventData: any;
			let dateText: string = "";
			let timeText: string = "";

			// Handle different item structures (direct items vs event items)
			if (item.event) {
				// This is an upcoming event from getEventsV2
				eventData = item.event;
				id = eventData.itemId || item._kenticoId || "";
				title = eventData.name || "";
				imageUrl = eventData.poster || "";
				itemType = "event";
			} else if (item.itemType === "event" || item.startDate) {
				// This is a direct event item
				id = item.itemId || item.id || "";
				title = item.name || "";
				imageUrl = item.poster || "";
				itemType = "event";
			} else {
				// This is a direct item from the section
				itemType = item.itemType || "video";
				id = item.itemId || item.id || "";
				title = item.name || item.title || "";
				imageUrl =
					item.poster ||
					(item.thumbnail ? String(item.thumbnail) : "");
			}

			const key = `${
				isLiveItem ? "live" : "upcoming"
			}-${itemType}-${id}`;

			// Format date and time for display
			let startDate: Date | null = null;

			// Get the start date from the appropriate property based on item structure
			if (item.event?.startDate) {
				startDate = new Date(item.event.startDate);
			} else if (item.startDate) {
				startDate = new Date(item.startDate);
			}

			if (startDate) {
				// Use localized date and time formatting
				dateText = formatLocalizedDate(startDate, currentLanguage);
				timeText = formatLocalizedTime(startDate, currentLanguage);
			}

			// Format metadata as a string (for backward compatibility)
			let metadataStr = "";

			// Add date and time to metadata
			if (dateText && timeText) {
				metadataStr = `${dateText} • ${timeText}`;
			}

			// Add views if available
			if (item.views) {
				metadataStr += metadataStr
					? ` • ${item.views} views`
					: `${item.views} views`;
			}

			// Optimize the image URL to prevent memory issues
			const optimizedImageUrl = imageUrl
				? optimizeImageUrl(imageUrl, "OPTIMIZED")
				: null;

			return {
				id,
				key,
				title,
				imageUrl: optimizedImageUrl || imageUrl, // Use optimized URL if available, fall back to original
				type: itemType,
				metadata: metadataStr,
				startDate: item.event?.startDate || item.startDate || null,
				dateText,
				timeText,
				isLive: isLiveItem,
			};
		});
	};

	// Filter and identify live events from the combined data
	// Items from liveItems array should be marked as live
	const mappedLiveItems = mapItemsForDisplay(liveItems, true);

	// Items from upcomingEvents array should be marked as upcoming
	const mappedUpcomingItems =
		upcomingEvents.length > 0
			? mapItemsForDisplay(upcomingEvents, false)
			: [];

	// Get spacing for items
	const getSpacing = () => {
		return scale(12);
	};

	// Render the card
	const renderCard = ({ item }: { item: any }) => {
		// Create event data object to pass to the onPress handler
		const eventData = {
			name: item.title,
			poster: item.imageUrl,
			description: "",
			isLive: item.isLive,
			startDate: item.startDate,
		};

		return (
			<LiveVideoCard
				id={item.id}
				title={item.title}
				imageUrl={item.imageUrl}
				onPress={(id) => onPress?.(id, item.type, eventData)}
				width={COMING_LIVE_ITEM_WIDTH}
				height={COMING_LIVE_ITEM_HEIGHT}
				date={item.dateText}
				time={item.timeText}
				isLive={item.isLive}
			/>
		);
	};

	// Get the section title
	const sectionTitle = sectionData.title || "";

	// Combine live and upcoming events, with live events appearing first
	// If this is the custom section after competitions, use only upcoming events
	// to avoid duplicates with the regular live section
	const isCustomSection =
		sectionData._kenticoCodename === "home_page___upcoming_matches";

	// Sort and prioritize events based on dynamic criteria
	const prioritizeEvents = (items: any[]) => {
		if (!items || items.length === 0) {
			return [];
		}

		// Create a copy of the array to avoid mutating the original
		const sortedItems = [...items];

		// Sort the items:
		// 1. Live events first
		// 2. Then upcoming events sorted by start time (earlier first)
		sortedItems.sort((a, b) => {
			// If one is live and the other isn't, the live one comes first
			if (a.isLive && !b.isLive) return -1;
			if (!a.isLive && b.isLive) return 1;

			// If both are live or both are upcoming, sort by start time
			// Parse the startDate strings into Date objects for comparison
			const aDate = a.startDate ? new Date(a.startDate) : null;
			const bDate = b.startDate ? new Date(b.startDate) : null;

			// Handle cases where dates might be missing
			if (aDate && !bDate) return -1;
			if (!aDate && bDate) return 1;
			if (!aDate && !bDate) return 0;

			// Sort by date (earlier first)
			// We've already checked that both aDate and bDate are not null above
			return aDate!.getTime() - bDate!.getTime();
		});

		return sortedItems;
	};

	// For the custom section, only use upcoming events
	// For regular sections, use both live and upcoming events
	let displayItems = isCustomSection
		? mappedUpcomingItems
		: [...mappedLiveItems, ...mappedUpcomingItems];

	// Apply prioritization to the display items
	displayItems = prioritizeEvents(displayItems);

	// Log the final count of items to be displayed
	console.log(
		`LiveSectionRenderer - displayed items count: ${displayItems.length}`
	);

	// Check if there are any items to display
	if (displayItems.length === 0) {
		return null;
	}

	return (
		<View
			style={styles.container}
			accessible={true}
			accessibilityLabel={
				accessibilityLabel ||
				`Live Section: ${sectionTitle || "Live Matches"}`
			}
		>
			{/* Section header with the title - custom style to align with content */}
			<View style={styles.sectionHeader}>
				<Text style={styles.sectionTitle}>{sectionTitle}</Text>
			</View>

			{/* Live or Upcoming Carousel */}
			{displayItems.length > 0 ? (
				<FlatList
					data={displayItems}
					horizontal
					showsHorizontalScrollIndicator={false}
					keyExtractor={(item) => {
						// First try to use the pre-generated key, then fall back to id with a prefix
						return item.key || `${item.type || "item"}-${item.id}`;
					}}
					contentContainerStyle={styles.list}
					ItemSeparatorComponent={() => (
						<View style={{ width: getSpacing() }} />
					)}
					renderItem={renderCard}
					// Performance optimizations
					removeClippedSubviews={true}
					maxToRenderPerBatch={5}
					windowSize={5}
					initialNumToRender={5}
				/>
			) : (
				<View style={styles.noContentContainer}>
					<Text style={styles.noLiveText}>
						No matches available at the moment
					</Text>
				</View>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
		// No horizontal padding as it will be handled by the parent container
	},
	sectionHeader: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		marginBottom: scale(8),
	},
	sectionTitle: {
		fontSize: scale(38),
		fontWeight: "bold",
		color: "#f9f9f9",
		marginBottom: scale(5),
	},
	list: {
		paddingRight: scale(24),
	},
	noContentContainer: {
		paddingVertical: scale(40),
		alignItems: "flex-start",
		justifyContent: "center",
		flexDirection: "row",
	},
	noLiveText: {
		color: "#fff",
		fontSize: scale(18),
		textAlign: "left",
		paddingVertical: scale(20),
		marginLeft: scale(15),
	},
});

export default React.memo(LiveSectionRenderer);
