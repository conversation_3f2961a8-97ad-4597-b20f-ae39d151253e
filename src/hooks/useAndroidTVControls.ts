// hooks/useAndroidTVControls.ts

import { useCallback, useEffect, useRef, useState } from "react";

import { Platform } from "react-native";

import type { VideoRef } from "react-native-video";

// ============================================================================

// HOOK

// ============================================================================

interface UseAndroidTVControlsProps {
	videoRef: React.RefObject<VideoRef>;

	initialPaused?: boolean;

	initialShowControls?: boolean;
}

interface UseAndroidTVControlsReturn {
	// State exposed to parent

	showControls: boolean;

	paused: boolean;

	currentTime: number;

	duration: number;

	seeking: boolean;

	focusedControl:
		| "playPause"
		| "rewind"
		| "forward"
		| "seekBar"
		| null;

	// Handlers to pass to parent Video component

	onPlay: () => void;

	onPause: () => void;

	onProgress: (data: { currentTime: number }) => void;

	onLoad: (data: { duration: number }) => void;

	onSeek: (data: { currentTime: number }) => void; // Callback from Video component

	// Handlers to pass to AndroidTVCustomControls component

	handlePlayPausePress: () => void;

	handleSeek: (time: number) => void; // Seek to absolute time

	handleRewind: () => void;

	handleForward: () => void;

	handleGlobalKeyPress: (event: any) => void;

	resetControlsTimeout: () => void; // Allow parent to reset timeout

	isAndroidTV: boolean;
}

const useAndroidTVControls = ({
	videoRef,

	initialPaused = true,

	initialShowControls = true,
}: UseAndroidTVControlsProps): UseAndroidTVControlsReturn => {
	const isAndroidTV = Platform.isTV && Platform.OS === "android";

	// Internal state managed by the hook

	const [showControls, setShowControls] = useState(
		initialShowControls
	);

	const [paused, setPaused] = useState(initialPaused);

	const [currentTime, setCurrentTime] = useState(0);

	const [duration, setDuration] = useState(0);

	const [seeking, setSeeking] = useState(false);

	const [focusedControl, setFocusedControl] = useState<
		"playPause" | "rewind" | "forward" | "seekBar" | null
	>("playPause");

	const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	const wasPlayingBeforeSeekRef = useRef<boolean>(!initialPaused);

	const isCurrentlyPlayingRef = useRef<boolean>(!initialPaused);

	// Auto-hide controls after 5 seconds of inactivity

	const resetControlsTimeout = useCallback(() => {
		// Always clear any existing timeouts first

		if (controlsTimeoutRef.current) {
			console.log(
				"[useAndroidTVControls] Clearing existing controls timeout"
			);

			clearTimeout(controlsTimeoutRef.current);

			controlsTimeoutRef.current = null; // Explicitly nullify ref
		}

		if (focusTimeoutRef.current) {
			console.log(
				"[useAndroidTVControls] Clearing existing focus timeout (if any)"
			);

			clearTimeout(focusTimeoutRef.current);

			// Do NOT nullify focusTimeoutRef here as it might be used by rewind/forward
		}

		// Always set the timer - the caller ensures showControls is true

		console.log(
			"[useAndroidTVControls] Setting/restarting controls auto-hide timer (5 seconds)"
		);

		controlsTimeoutRef.current = setTimeout(() => {
			console.log(
				"[useAndroidTVControls] Auto-hiding controls after 5-second timeout"
			);

			setShowControls(false);

			setFocusedControl(null);
		}, 5000);
	}, []); // Dependencies removed as it only interacts with refs and setters

	// Unified play/pause handler

	const handlePlayPausePress = useCallback(() => {
		console.log(
			`[useAndroidTVControls] Play/Pause button pressed - Controls visible: ${showControls}, Paused: ${paused}`
		);

		setShowControls(true); // Always show on interaction

		resetControlsTimeout();

		if (videoRef.current) {
			const newPausedState = !paused;

			if (newPausedState) {
				console.log("[useAndroidTVControls] Sending pause command");

				videoRef.current.pause();
			} else {
				console.log("[useAndroidTVControls] Sending play command");

				videoRef.current.resume();
			}

			setPaused(newPausedState);

			isCurrentlyPlayingRef.current = !newPausedState; // Update internal ref
		}

		setFocusedControl("playPause"); // Ensure focus returns to play/pause
	}, [paused, videoRef, resetControlsTimeout]);

	// Handle seeking to an absolute time

	const handleSeek = useCallback(
		(time: number) => {
			if (videoRef.current) {
				console.log(`[useAndroidTVControls] Seeking to ${time}s`);

				setSeeking(true);

				wasPlayingBeforeSeekRef.current =
					isCurrentlyPlayingRef.current; // Capture state before seek

				videoRef.current.seek(time);

				setCurrentTime(time);

				// Reset auto-hide timer on seek interaction

				resetControlsTimeout();
			}
		},

		[videoRef, resetControlsTimeout]
	);

	// Rewind Handler

	const handleRewind = useCallback(() => {
		if (videoRef.current) {
			const seekAmount = 10; // seconds

			const newTime = Math.max(0, currentTime - seekAmount);

			wasPlayingBeforeSeekRef.current = isCurrentlyPlayingRef.current;

			console.log(
				`[useAndroidTVControls] Rewinding to ${newTime}s. Was playing (from ref): ${wasPlayingBeforeSeekRef.current}`
			);

			videoRef.current.seek(newTime);

			setCurrentTime(newTime);

			setSeeking(true);

			setShowControls(true); // Show controls on interaction

			resetControlsTimeout();

			setFocusedControl("rewind");

			// Clear any existing focus timeout

			if (focusTimeoutRef.current) {
				clearTimeout(focusTimeoutRef.current);
			}

			// Set timer to return focus to play/pause after 2 seconds

			focusTimeoutRef.current = setTimeout(() => {
				setFocusedControl("playPause");
			}, 2000);
		}
	}, [currentTime, videoRef, resetControlsTimeout]);

	// Forward Handler

	const handleForward = useCallback(() => {
		if (videoRef.current) {
			const seekAmount = 10; // seconds

			const newTime = Math.min(duration, currentTime + seekAmount);

			wasPlayingBeforeSeekRef.current = isCurrentlyPlayingRef.current;

			console.log(
				`[useAndroidTVControls] Fast forwarding to ${newTime}s. Was playing (from ref): ${wasPlayingBeforeSeekRef.current}`
			);

			videoRef.current.seek(newTime);

			setCurrentTime(newTime);

			setSeeking(true);

			setShowControls(true); // Show controls on interaction

			resetControlsTimeout();

			setFocusedControl("forward");

			if (focusTimeoutRef.current) {
				clearTimeout(focusTimeoutRef.current);
			}

			focusTimeoutRef.current = setTimeout(() => {
				setFocusedControl("playPause");
			}, 2000);
		}
	}, [currentTime, duration, videoRef, resetControlsTimeout]);

	// Sync with native player events

	const onPlay = useCallback(() => {
		console.log(
			"[useAndroidTVControls] Native player played/resumed"
		);

		setPaused(false);

		isCurrentlyPlayingRef.current = true;
	}, []);

	const onPause = useCallback(() => {
		console.log("[useAndroidTVControls] Native player paused");

		setPaused(true);

		isCurrentlyPlayingRef.current = false;
	}, []);

	const onProgress = useCallback(
		(data: { currentTime: number }) => {
			if (!seeking) {
				setCurrentTime(data.currentTime);
			}
		},

		[seeking]
	);

	const onLoad = useCallback(
		(data: { duration: number }) => {
			console.log(
				"[useAndroidTVControls] Video loaded, duration:",
				data.duration
			);

			setDuration(data.duration || 0);

			setShowControls(true);

			setFocusedControl("playPause");

			resetControlsTimeout();
		},

		[resetControlsTimeout]
	);

	const onSeek = useCallback(
		(data: { currentTime: number }) => {
			console.log(
				"[useAndroidTVControls] Seek completed to:",

				data.currentTime,

				"Was playing before seek (ref):",

				wasPlayingBeforeSeekRef.current
			);

			setSeeking(false);

			setCurrentTime(data.currentTime);

			// CRITICAL: Resume playback if video was playing before the seek operation

			if (wasPlayingBeforeSeekRef.current && videoRef.current) {
				console.log(
					"[useAndroidTVControls] Resuming playback after seek completion."
				);

				setPaused(false);

				isCurrentlyPlayingRef.current = true;

				try {
					videoRef.current.resume();
				} catch (e) {
					console.error(
						"[useAndroidTVControls] Error calling resume() after seek:",
						e
					);

					isCurrentlyPlayingRef.current = false;

					setPaused(true);
				}
			} else {
				// Ensure state is consistent if it was paused

				isCurrentlyPlayingRef.current = !paused;
			}

			// Reset the flag for the next potential seek

			wasPlayingBeforeSeekRef.current = false;

			resetControlsTimeout();
		},

		[videoRef, paused, resetControlsTimeout]
	);

	// Global key event handler (Note: TVEventHandler limitations)

	const handleGlobalKeyPress = useCallback(
		(event: any) => {
			if (!isAndroidTV) return;

			const keyCode = event?.keyCode || event?.which;

			if (keyCode === 23 || keyCode === 66) {
				// D-Pad Center or Enter

				handlePlayPausePress();

				return;
			}

			if (keyCode === 21) {
				// D-Pad Left

				handleRewind();

				return;
			}

			if (keyCode === 22) {
				// D-Pad Right

				handleForward();

				return;
			}

			// Any other key shows controls

			if (!showControls) {
				setShowControls(true);

				resetControlsTimeout();
			}
		},

		[
			isAndroidTV,
			handlePlayPausePress,
			handleRewind,
			handleForward,
			showControls,
			resetControlsTimeout,
		]
	);

	// Cleanup timeouts on unmount

	useEffect(() => {
		return () => {
			if (controlsTimeoutRef.current) {
				clearTimeout(controlsTimeoutRef.current);
			}

			if (focusTimeoutRef.current) {
				clearTimeout(focusTimeoutRef.current);
			}
		};
	}, []);

	return {
		// State

		showControls,

		paused,

		currentTime,

		duration,

		seeking,

		focusedControl,

		// Handlers for Video component

		onPlay,

		onPause,

		onProgress,

		onLoad,

		onSeek,

		// Handlers for AndroidTVCustomControls component

		handlePlayPausePress,

		handleSeek,

		handleRewind,

		handleForward,

		handleGlobalKeyPress,

		resetControlsTimeout,

		// Utility

		isAndroidTV,
	};
};

export default useAndroidTVControls;
