import { scale } from "../utils/helpers/dimensionScale.helper";

/**
 * Global style constants to maintain consistency across the app
 */
export const GLOBAL_STYLES = {
	// Standard page padding (16 units scaled)
	PAGE_HORIZONTAL_PADDING: scale(24),

	// Standard spacing between items in lists
	ITEM_SPACING: scale(16), // spacing between cards

	// Standard margin between sections
	SECTION_MARGIN_BOTTOM: scale(24),

	// Standard border radius
	BORDER_RADIUS: scale(10),

	// Standard colors
	COLORS: {
		// Primary background color for all pages
		PAGE_BACKGROUND: "#102e55",

		// Text colors
		TEXT_PRIMARY: "#FFFFFF",
		TEXT_SECONDARY: "rgba(255, 255, 255, 0.8)",
		TEXT_TERTIARY: "rgba(255, 255, 255, 0.6)",

		// Accent colors
		ACCENT: "#ff0000",
	},

	// Focus styling constants for consistent white border implementation
	FOCUS: {
		BORDER_WIDTH: scale(6), // Standard border width for focused elements - increased by 1px for better visibility
		BORDER_COLOR_FOCUSED: "#ffffff", // White border color for focused state
		BORDER_COLOR_UNFOCUSED: "transparent", // Transparent border for unfocused state to prevent layout shifts
		// All elements should have transparent borders by default to ensure proper spacing
		// and prevent visual artifacts when borders appear on focus
	},
};
